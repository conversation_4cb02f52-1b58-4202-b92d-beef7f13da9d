<template>
  <div class="app-container">
    <MainLayout />
  </div>
</template>

<script setup>
import MainLayout from './views/MainLayout.vue';
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #app {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

.app-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  margin: 0;
  padding: 0;
}

/* 重置Element Plus的一些默认样式 */
.el-card {
  --el-card-padding: 15px;
  margin: 0;
}

.el-button {
  --el-button-size: 40px;
}
</style>