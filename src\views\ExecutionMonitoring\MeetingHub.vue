<template>
  <div class="meeting-hub">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><ChatRound /></el-icon>
        会议中心
      </h1>
      <p class="page-description">
        "中枢神经系统"，将所有会议安排流程整合为高度自动化、标准化的集中式管理模块。
      </p>
    </div>

    <div class="hub-content">
      <div class="meetings-overview">
        <el-row :gutter="24">
          <el-col :span="16">
            <el-card class="meetings-card">
              <template #header>
                <div class="card-header">
                  <h3>即将召开的会议</h3>
                  <el-button type="primary" @click="scheduleMeeting">
                    <el-icon><Plus /></el-icon>
                    安排会议
                  </el-button>
                </div>
              </template>
              
              <div class="meetings-list">
                <div 
                  v-for="meeting in upcomingMeetings" 
                  :key="meeting.id"
                  class="meeting-item"
                >
                  <div class="meeting-info">
                    <h4>{{ meeting.title }}</h4>
                    <p>{{ meeting.date }} {{ meeting.time }} | {{ meeting.duration }}分钟</p>
                    <p>参会人员：{{ meeting.attendees.join(', ') }}</p>
                  </div>
                  <div class="meeting-status">
                    <el-tag 
                      :type="getStatusType(meeting.status)" 
                      size="small"
                    >
                      {{ getStatusText(meeting.status) }}
                    </el-tag>
                  </div>
                  <div class="meeting-actions">
                    <el-button type="primary" size="small" @click="joinMeeting(meeting)">
                      加入会议
                    </el-button>
                    <el-button type="info" size="small" @click="editMeeting(meeting)">
                      编辑
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="templates-card">
              <template #header>
                <h3>会议模板</h3>
              </template>
              
              <div class="templates-list">
                <div 
                  v-for="template in meetingTemplates" 
                  :key="template.id"
                  class="template-item"
                  @click="useTemplate(template)"
                >
                  <div class="template-info">
                    <h5>{{ template.name }}</h5>
                    <p>{{ template.duration }}分钟</p>
                  </div>
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ChatRound, Plus, ArrowRight } from '@element-plus/icons-vue'
import { executionMonitoringApi } from '@/api/pmpApi'

// 响应式数据
const upcomingMeetings = ref([])
const meetingTemplates = ref([])

// 页面挂载时加载数据
onMounted(() => {
  loadMeetingData()
})

// 加载会议数据
const loadMeetingData = async () => {
  try {
    const response = await executionMonitoringApi.getMeetingHub()
    if (response.code === 200) {
      upcomingMeetings.value = response.data.upcomingMeetings
      meetingTemplates.value = response.data.meetingTemplates
    }
  } catch (error) {
    ElMessage.error('加载会议数据失败')
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    scheduled: 'success',
    in-progress: 'primary',
    completed: 'info',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    scheduled: '已安排',
    'in-progress': '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

// 安排会议
const scheduleMeeting = () => {
  ElMessage.info('安排会议功能正在开发中...')
}

// 加入会议
const joinMeeting = (meeting) => {
  ElMessage.info(`正在加入会议"${meeting.title}"...`)
}

// 编辑会议
const editMeeting = (meeting) => {
  ElMessage.info(`编辑会议"${meeting.title}"...`)
}

// 使用模板
const useTemplate = (template) => {
  ElMessage.info(`使用模板"${template.name}"创建会议...`)
}
</script>

<style scoped>
.meeting-hub {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.hub-content {
  max-width: 1200px;
  margin: 0 auto;
}

.meetings-card, .templates-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.meetings-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.meeting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.meeting-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
}

.meeting-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.meeting-actions {
  display: flex;
  gap: 8px;
}

.templates-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.template-info h5 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 14px;
}

.template-info p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

@media (max-width: 768px) {
  .meeting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .meeting-actions {
    justify-content: center;
  }
}
</style>
