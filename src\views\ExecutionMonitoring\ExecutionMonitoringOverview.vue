<template>
  <div class="execution-monitoring-overview">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Monitor /></el-icon>
        执行与监控面板
      </h1>
      <p class="page-description">
        这是项目运行时的"驾驶舱"，让小白也能轻松掌控项目状态。
      </p>
    </div>

    <div class="overview-content">
      <!-- 项目健康状态总览 -->
      <div class="health-overview">
        <el-card class="health-card">
          <div class="health-indicator">
            <div class="health-light" :class="projectHealth">
              <div class="light-inner"></div>
            </div>
            <div class="health-info">
              <h2 class="health-title">项目健康状态</h2>
              <p class="health-status">{{ getHealthText(projectHealth) }}</p>
              <p class="health-desc">{{ getHealthDescription(projectHealth) }}</p>
            </div>
          </div>
          
          <div class="progress-comparison">
            <div class="progress-item">
              <span class="progress-label">计划进度</span>
              <el-progress 
                :percentage="plannedProgress" 
                color="#409eff"
                :stroke-width="8"
              />
            </div>
            <div class="progress-item">
              <span class="progress-label">实际进度</span>
              <el-progress 
                :percentage="actualProgress" 
                :color="getProgressColor(actualProgress, plannedProgress)"
                :stroke-width="8"
              />
            </div>
          </div>
        </el-card>
      </div>

      <!-- 功能模块网格 -->
      <div class="module-grid">
        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('health-status-dashboard')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><TrendCharts /></el-icon>
            </div>
            <h3 class="card-title">健康状态仪表盘</h3>
            <p class="card-description">
              实时、自动、可视化的决策支持中心，包含项目总览、任务健康、
              障碍优先级和关键路径四个核心视图。
            </p>
            <div class="card-stats">
              <el-tag type="danger" size="small">{{ criticalTasks }}个关键任务</el-tag>
              <el-tag type="warning" size="small">{{ obstacles }}个障碍</el-tag>
            </div>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('status-reports')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><Document /></el-icon>
            </div>
            <h3 class="card-title">每日/每周状态报告</h3>
            <p class="card-description">
              高度自动化的报告编制流程，包含每日状态报告和每周项目状态报告，
              确保信息准确传递。
            </p>
            <div class="card-stats">
              <el-tag type="success" size="small">今日已提交</el-tag>
              <el-tag type="info" size="small">本周第{{ weekDay }}天</el-tag>
            </div>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('obstacle-management')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><Warning /></el-icon>
            </div>
            <h3 class="card-title">障碍管理</h3>
            <p class="card-description">
              "危机处理中心"，将复杂的障碍管理流程转化为强制性、防错的闭环工作流，
              从发现到评估、批准、解决。
            </p>
            <div class="card-stats">
              <el-tag type="danger" size="small">{{ imminentObstacles }}个迫在眉睫</el-tag>
              <el-tag type="warning" size="small">{{ totalObstacles }}个总计</el-tag>
            </div>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('meeting-hub')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><ChatRound /></el-icon>
            </div>
            <h3 class="card-title">会议中心</h3>
            <p class="card-description">
              "中枢神经系统"，将所有会议安排流程整合为高度自动化、标准化的
              集中式管理模块。
            </p>
            <div class="card-stats">
              <el-tag type="primary" size="small">{{ upcomingMeetings }}个即将召开</el-tag>
              <el-tag type="success" size="small">{{ meetingTemplates }}个模板</el-tag>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 关键指标 -->
      <div class="key-metrics">
        <h3 class="section-title">关键指标</h3>
        <div class="metrics-grid">
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon size="32" color="#f56c6c"><Clock /></el-icon>
            </div>
            <div class="metric-info">
              <h4>关键路径任务</h4>
              <p class="metric-value">{{ criticalPathTasks }}</p>
              <p class="metric-desc">时间裕量 ≤ 10天</p>
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon size="32" color="#e6a23c"><WarningFilled /></el-icon>
            </div>
            <div class="metric-info">
              <h4>延期风险</h4>
              <p class="metric-value">{{ delayRisk }}%</p>
              <p class="metric-desc">基于当前进度分析</p>
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon size="32" color="#67c23a"><SuccessFilled /></el-icon>
            </div>
            <div class="metric-info">
              <h4>完成率</h4>
              <p class="metric-value">{{ completionRate }}%</p>
              <p class="metric-desc">总体项目进度</p>
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon size="32" color="#409eff"><User /></el-icon>
            </div>
            <div class="metric-info">
              <h4>团队成员</h4>
              <p class="metric-value">{{ teamMembers }}</p>
              <p class="metric-desc">活跃参与人数</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h3 class="section-title">快速操作</h3>
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="viewDashboard">
            <el-icon><TrendCharts /></el-icon>
            查看仪表盘
          </el-button>
          <el-button type="success" size="large" @click="createReport">
            <el-icon><EditPen /></el-icon>
            创建状态报告
          </el-button>
          <el-button type="warning" size="large" @click="manageObstacles">
            <el-icon><Warning /></el-icon>
            管理障碍
          </el-button>
          <el-button type="info" size="large" @click="scheduleMeeting">
            <el-icon><Calendar /></el-icon>
            安排会议
          </el-button>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="recent-activities">
        <h3 class="section-title">最近活动</h3>
        <el-card>
          <div class="activity-list">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon :color="activity.color">
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-text">{{ activity.text }}</p>
                <p class="activity-time">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Monitor, TrendCharts, Document, Warning, ChatRound, Clock, 
  WarningFilled, SuccessFilled, User, EditPen, Calendar 
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const projectHealth = ref('yellow') // green, yellow, red
const plannedProgress = ref(65)
const actualProgress = ref(58)
const criticalTasks = ref(3)
const obstacles = ref(2)
const weekDay = ref(3)
const imminentObstacles = ref(1)
const totalObstacles = ref(4)
const upcomingMeetings = ref(2)
const meetingTemplates = ref(8)
const criticalPathTasks = ref(5)
const delayRisk = ref(25)
const completionRate = ref(58)
const teamMembers = ref(8)
const recentActivities = ref([
  {
    id: 1,
    icon: 'SuccessFilled',
    color: '#67c23a',
    text: '任务"系统集成测试"已完成',
    time: '2小时前'
  },
  {
    id: 2,
    icon: 'WarningFilled',
    color: '#e6a23c',
    text: '障碍"服务器硬件延期"状态更新为迫在眉睫',
    time: '4小时前'
  },
  {
    id: 3,
    icon: 'Document',
    color: '#409eff',
    text: '每日状态报告已提交',
    time: '昨天 20:00'
  },
  {
    id: 4,
    icon: 'ChatRound',
    color: '#909399',
    text: '项目状态评审会议已安排',
    time: '昨天 16:30'
  }
])

// 页面挂载时加载数据
onMounted(() => {
  loadOverviewData()
})

// 加载概览数据
const loadOverviewData = () => {
  // 模拟从API获取数据
  setTimeout(() => {
    // 数据已在初始化时设置
  }, 500)
}

// 获取健康状态文本
const getHealthText = (health) => {
  const texts = {
    green: '健康',
    yellow: '注意',
    red: '危险'
  }
  return texts[health] || '未知'
}

// 获取健康状态描述
const getHealthDescription = (health) => {
  const descriptions = {
    green: '项目按计划进行，无重大风险',
    yellow: '存在潜在风险，需要关注',
    red: '项目面临严重风险，需要立即行动'
  }
  return descriptions[health] || ''
}

// 获取进度条颜色
const getProgressColor = (actual, planned) => {
  if (actual >= planned) return '#67c23a'
  if (actual >= planned - 10) return '#e6a23c'
  return '#f56c6c'
}

// 导航到模块
const navigateToModule = (module) => {
  router.push(`/execution-monitoring/${module}`)
}

// 快速操作方法
const viewDashboard = () => {
  router.push('/execution-monitoring/health-status-dashboard')
}

const createReport = () => {
  router.push('/execution-monitoring/status-reports')
}

const manageObstacles = () => {
  router.push('/execution-monitoring/obstacle-management')
}

const scheduleMeeting = () => {
  router.push('/execution-monitoring/meeting-hub')
}
</script>

<style scoped>
.execution-monitoring-overview {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.overview-content {
  max-width: 1200px;
  margin: 0 auto;
}

.health-overview {
  margin-bottom: 32px;
}

.health-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.health-light {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.health-light.green {
  background-color: #67c23a;
  box-shadow: 0 0 20px rgba(103, 194, 58, 0.6);
}

.health-light.yellow {
  background-color: #e6a23c;
  box-shadow: 0 0 20px rgba(230, 162, 60, 0.6);
}

.health-light.red {
  background-color: #f56c6c;
  box-shadow: 0 0 20px rgba(245, 108, 108, 0.6);
}

.light-inner {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.health-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
}

.health-status {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.health-desc {
  margin: 0;
  opacity: 0.9;
}

.progress-comparison {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.progress-label {
  min-width: 80px;
  font-weight: 500;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.module-card:hover {
  transform: translateY(-4px);
  border-color: #409eff;
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.card-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
  font-size: 14px;
}

.card-stats {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.key-metrics, .quick-actions, .recent-activities {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.metric-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin: 0 0 4px 0;
}

.metric-desc {
  color: #606266;
  font-size: 12px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 14px;
}

.activity-time {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

@media (max-width: 768px) {
  .health-indicator {
    flex-direction: column;
    text-align: center;
  }
  
  .progress-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .module-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
