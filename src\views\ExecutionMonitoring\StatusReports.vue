<template>
  <div class="status-reports">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Document /></el-icon>
        每日/每周状态报告
      </h1>
      <p class="page-description">
        高度自动化的报告编制流程，确保信息准确传递。
      </p>
    </div>

    <div class="reports-content">
      <el-tabs v-model="activeTab" class="reports-tabs">
        <!-- 每日状态报告 -->
        <el-tab-pane label="每日状态报告" name="daily">
          <div class="daily-reports">
            <div class="report-form">
              <h3>今日状态报告</h3>
              <el-form :model="dailyReport" label-width="120px">
                <el-form-item label="三个感恩">
                  <div class="gratitude-list">
                    <div v-for="(gratitude, index) in dailyReport.gratitudes" :key="index" class="gratitude-item">
                      <el-input
                        v-model="dailyReport.gratitudes[index]"
                        placeholder="请输入具体的、积极的事件..."
                        maxlength="100"
                        show-word-limit
                      />
                    </div>
                  </div>
                </el-form-item>
                
                <el-form-item label="今天的成就">
                  <div class="achievement-list">
                    <div v-for="(achievement, index) in dailyReport.achievements" :key="index" class="achievement-item">
                      <el-input
                        v-model="dailyReport.achievements[index]"
                        placeholder="请输入推动项目的已完成事项..."
                        maxlength="100"
                        show-word-limit
                      />
                    </div>
                  </div>
                </el-form-item>
                
                <el-form-item label="明天的计划">
                  <div class="plan-list">
                    <div v-for="(plan, index) in dailyReport.tomorrowPlans" :key="index" class="plan-item">
                      <el-input
                        v-model="dailyReport.tomorrowPlans[index]"
                        placeholder="请输入第二天的具体计划..."
                        maxlength="100"
                        show-word-limit
                      />
                    </div>
                  </div>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="submitDailyReport">
                    提交每日报告
                  </el-button>
                  <el-button @click="saveDraft">
                    保存草稿
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-tab-pane>

        <!-- 每周状态报告 -->
        <el-tab-pane label="每周状态报告" name="weekly">
          <div class="weekly-reports">
            <div class="report-form">
              <h3>本周状态报告</h3>
              <el-form :model="weeklyReport" label-width="120px">
                <el-form-item label="项目健康状态">
                  <el-select v-model="weeklyReport.projectHealth" placeholder="选择健康状态">
                    <el-option label="绿色 - 健康" value="green" />
                    <el-option label="黄色 - 注意" value="yellow" />
                    <el-option label="红色 - 危险" value="red" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="完成百分比">
                  <el-input-number 
                    v-model="weeklyReport.completionPercentage" 
                    :min="0" 
                    :max="100"
                    :precision="0"
                  />
                  <span style="margin-left: 8px;">%</span>
                </el-form-item>
                
                <el-form-item label="状态摘要">
                  <el-input
                    v-model="weeklyReport.statusSummary"
                    type="textarea"
                    :rows="4"
                    placeholder="请描述项目整体状态..."
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="submitWeeklyReport">
                    提交周报告
                  </el-button>
                  <el-button @click="generateReport">
                    自动生成
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('daily')
const dailyReport = ref({
  gratitudes: ['', '', ''],
  achievements: ['', '', ''],
  tomorrowPlans: ['', '', '']
})
const weeklyReport = ref({
  projectHealth: '',
  completionPercentage: 0,
  statusSummary: ''
})

// 页面挂载时加载数据
onMounted(() => {
  loadReportsData()
})

// 加载报告数据
const loadReportsData = () => {
  // 模拟加载数据
  ElMessage.info('报告数据加载完成')
}

// 提交每日报告
const submitDailyReport = () => {
  ElMessage.success('每日报告已提交')
}

// 保存草稿
const saveDraft = () => {
  ElMessage.info('草稿已保存')
}

// 提交周报告
const submitWeeklyReport = () => {
  ElMessage.success('周报告已提交')
}

// 自动生成报告
const generateReport = () => {
  ElMessage.info('正在自动生成报告...')
}
</script>

<style scoped>
.status-reports {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.reports-content {
  max-width: 800px;
  margin: 0 auto;
}

.reports-tabs {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.report-form h3 {
  color: #2c3e50;
  margin-bottom: 24px;
}

.gratitude-list, .achievement-list, .plan-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gratitude-item, .achievement-item, .plan-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .reports-content {
    max-width: 100%;
  }
  
  .reports-tabs {
    padding: 16px;
  }
}
</style>
