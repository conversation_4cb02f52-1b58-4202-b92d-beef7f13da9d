<template>
  <div class="client-method-discovery">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Search /></el-icon>
          客户方法发现会议
        </h1>
        <div class="step-indicator">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="收集信息" />
            <el-step title="确认内容" />
            <el-step title="发布会议" />
          </el-steps>
        </div>
      </div>
    </div>

    <div class="discovery-content">
      <!-- 步骤1: 收集信息 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-card class="step-card">
          <template #header>
            <div class="card-header">
              <h3>确定客户方法要求的所有标准可交付成果</h3>
              <el-button type="info" size="small" circle @click="showHelp('deliverables')">
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="deliverables-section">
            <p class="section-description">
              请勾选项目需要的标准可交付成果，这是完成计划进度表的前提。
            </p>
            
            <div class="deliverables-grid">
              <div 
                v-for="deliverable in standardDeliverables" 
                :key="deliverable.id"
                class="deliverable-item"
                :class="{ 'selected': deliverable.selected }"
              >
                <el-checkbox 
                  v-model="deliverable.selected"
                  @change="onDeliverableChange(deliverable)"
                >
                  {{ deliverable.name }}
                </el-checkbox>
                
                <div v-if="deliverable.selected" class="purpose-input">
                  <el-input
                    v-model="deliverable.purpose"
                    type="textarea"
                    :rows="2"
                    placeholder="请说明这个交付物对项目成功有何帮助..."
                    maxlength="200"
                    show-word-limit
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="step-card">
          <template #header>
            <h3>获取相关说明文档和模板</h3>
          </template>
          
          <div class="documents-section">
            <div class="upload-area">
              <h4>上传说明文档</h4>
              <el-upload
                class="upload-demo"
                drag
                :auto-upload="false"
                multiple
                :file-list="uploadedDocuments"
                @change="handleDocumentUpload"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将客户的内部方法论文档拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 PDF、Word、Excel 等格式文件
                  </div>
                </template>
              </el-upload>
            </div>

            <div class="upload-area">
              <h4>上传必需模板</h4>
              <el-upload
                class="upload-demo"
                drag
                :auto-upload="false"
                multiple
                :file-list="uploadedTemplates"
                @change="handleTemplateUpload"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将客户要求的模板文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    上传客户特定的 Word、Excel 模板
                  </div>
                </template>
              </el-upload>
            </div>

            <div class="template-notes">
              <h4>模板字段说明</h4>
              <el-input
                v-model="templateNotes"
                type="textarea"
                :rows="4"
                placeholder="请记录对模板字段的理解和备注，例如：'预算编号'栏需要填写客户ERP系统中的项目编码..."
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button type="primary" size="large" @click="nextStep" :disabled="!canProceed">
            下一步：确认内容
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 确认内容 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-card class="step-card">
          <template #header>
            <h3>确认收集的信息</h3>
          </template>
          
          <div class="confirmation-section">
            <div class="selected-deliverables">
              <h4>已选择的标准可交付成果</h4>
              <div class="deliverable-list">
                <div 
                  v-for="deliverable in selectedDeliverables" 
                  :key="deliverable.id"
                  class="deliverable-summary"
                >
                  <div class="deliverable-name">
                    <el-icon><Document /></el-icon>
                    {{ deliverable.name }}
                  </div>
                  <div class="deliverable-purpose">
                    <strong>目的：</strong>{{ deliverable.purpose }}
                  </div>
                </div>
              </div>
            </div>

            <div class="uploaded-files">
              <h4>已上传的文件</h4>
              <div class="file-summary">
                <div v-if="uploadedDocuments.length > 0">
                  <p><strong>说明文档：</strong>{{ uploadedDocuments.length }} 个文件</p>
                </div>
                <div v-if="uploadedTemplates.length > 0">
                  <p><strong>模板文件：</strong>{{ uploadedTemplates.length }} 个文件</p>
                </div>
                <div v-if="uploadedDocuments.length === 0 && uploadedTemplates.length === 0">
                  <el-alert 
                    title="提醒" 
                    type="warning" 
                    :closable="false"
                    show-icon
                  >
                    您没有上传任何文件，请确认是否已获取所有相关说明文档？
                  </el-alert>
                </div>
              </div>
            </div>

            <div v-if="templateNotes" class="notes-summary">
              <h4>模板字段说明</h4>
              <div class="notes-content">{{ templateNotes }}</div>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button size="large" @click="prevStep">
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button type="primary" size="large" @click="nextStep">
            下一步：安排会议
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤3: 发布会议 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-card class="step-card">
          <template #header>
            <h3>安排客户方法发现会议</h3>
          </template>
          
          <div class="meeting-section">
            <div class="meeting-info">
              <el-alert 
                title="重要提醒" 
                type="warning" 
                :closable="false"
                show-icon
              >
                项目管理者不得在客户在场的情况下，在工作计划中输入或修改任务。此步骤的目的是收集信息，而非现场创建。
              </el-alert>
            </div>

            <div class="meeting-form">
              <el-form :model="meetingForm" label-width="120px">
                <el-form-item label="会议主题">
                  <el-input 
                    v-model="meetingForm.subject" 
                    placeholder="[项目名称]:客户方法发现会议"
                  />
                </el-form-item>
                
                <el-form-item label="会议时间">
                  <el-date-picker
                    v-model="meetingForm.datetime"
                    type="datetime"
                    placeholder="选择会议时间"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm"
                  />
                </el-form-item>
                
                <el-form-item label="会议时长">
                  <el-select v-model="meetingForm.duration" placeholder="选择会议时长">
                    <el-option label="1小时" value="60" />
                    <el-option label="1.5小时" value="90" />
                    <el-option label="2小时" value="120" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="参会人员">
                  <el-input
                    v-model="meetingForm.attendees"
                    type="textarea"
                    :rows="3"
                    placeholder="请添加方法决策者/领域专家的邮箱地址，每行一个"
                  />
                </el-form-item>
                
                <el-form-item label="会议地点">
                  <el-input 
                    v-model="meetingForm.location" 
                    placeholder="会议室或在线会议链接"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button size="large" @click="prevStep">
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button type="primary" size="large" @click="createMeeting">
            <el-icon><Calendar /></el-icon>
            创建会议
          </el-button>
        </div>
      </div>

      <!-- 完成状态 -->
      <div v-if="currentStep === 3" class="completion-section">
        <el-result
          icon="success"
          title="客户方法发现会议已安排"
          sub-title="会议邀请已发送，请按时参加会议并收集所需信息"
        >
          <template #extra>
            <el-button type="primary" size="large" @click="goToPlanning">
              进入项目规划中心
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, QuestionFilled, ArrowRight, ArrowLeft, 
  UploadFilled, Document, Calendar 
} from '@element-plus/icons-vue'
import { projectKickoffApi } from '@/api/pmpApi'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const standardDeliverables = ref([])
const uploadedDocuments = ref([])
const uploadedTemplates = ref([])
const templateNotes = ref('')
const meetingForm = ref({
  subject: '',
  datetime: '',
  duration: '60',
  attendees: '',
  location: ''
})

// 计算属性
const selectedDeliverables = computed(() => {
  return standardDeliverables.value.filter(item => item.selected)
})

const canProceed = computed(() => {
  return selectedDeliverables.value.length > 0 && 
         selectedDeliverables.value.every(item => item.purpose.trim() !== '')
})

// 页面挂载时加载数据
onMounted(() => {
  loadDiscoveryData()
})

// 加载发现会议数据
const loadDiscoveryData = async () => {
  try {
    const response = await projectKickoffApi.getClientMethodDiscovery()
    if (response.code === 200) {
      standardDeliverables.value = response.data.standardDeliverables
      // 初始化会议主题
      meetingForm.value.subject = '[项目名称]:客户方法发现会议'
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 可交付成果变更处理
const onDeliverableChange = (deliverable) => {
  if (!deliverable.selected) {
    deliverable.purpose = ''
  }
}

// 文档上传处理
const handleDocumentUpload = (file, fileList) => {
  uploadedDocuments.value = fileList
}

// 模板上传处理
const handleTemplateUpload = (file, fileList) => {
  uploadedTemplates.value = fileList
}

// 显示帮助信息
const showHelp = (type) => {
  let content = ''
  if (type === 'deliverables') {
    content = '请务必在项目分配后的第二周结束前完成此发现。这是完成计划进度表的前提。'
  }
  ElMessageBox.alert(content, '帮助信息', {
    confirmButtonText: '知道了'
  })
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 创建会议
const createMeeting = () => {
  // 验证表单
  if (!meetingForm.value.subject || !meetingForm.value.datetime) {
    ElMessage.warning('请填写完整的会议信息')
    return
  }
  
  ElMessageBox.confirm(
    '确定要创建客户方法发现会议吗？',
    '确认创建',
    {
      confirmButtonText: '创建',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 模拟创建会议
    ElMessage.success('会议创建成功！邮件邀请已发送')
    currentStep.value = 3
  }).catch(() => {
    // 用户取消
  })
}

// 进入项目规划
const goToPlanning = () => {
  router.push('/project-planning')
}
</script>

<style scoped>
.client-method-discovery {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 24px 0;
  text-align: center;
  justify-content: center;
}

.discovery-content {
  max-width: 1000px;
  margin: 0 auto;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.section-description {
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.6;
}

.deliverables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.deliverable-item {
  padding: 16px;
  border: 2px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.deliverable-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.purpose-input {
  margin-top: 12px;
}

.documents-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-area h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.template-notes h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.confirmation-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.deliverable-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deliverable-summary {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.deliverable-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.deliverable-purpose {
  color: #606266;
  line-height: 1.5;
}

.file-summary p {
  margin: 8px 0;
  color: #606266;
}

.notes-content {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
}

.meeting-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.meeting-form {
  max-width: 600px;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
}

.completion-section {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .deliverables-grid {
    grid-template-columns: 1fr;
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .meeting-form {
    max-width: 100%;
  }
}
</style>
