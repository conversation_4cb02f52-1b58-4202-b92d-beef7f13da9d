import { createRouter, createWebHistory } from 'vue-router'

// 项目启动向导模块
const ProjectKickoffOverview = () => import('@/views/ProjectKickoff/ProjectKickoffOverview.vue')
const DayOneChecklist = () => import('@/views/ProjectKickoff/DayOneChecklist.vue')
const ClientMethodDiscovery = () => import('@/views/ProjectKickoff/ClientMethodDiscovery.vue')

// 项目规划中心模块
const ProjectPlanningOverview = () => import('@/views/ProjectPlanning/ProjectPlanningOverview.vue')
const WorkPlanBuilder = () => import('@/views/ProjectPlanning/WorkPlanBuilder.vue')
const ScheduleRelease = () => import('@/views/ProjectPlanning/ScheduleRelease.vue')
const RequiredDeliverables = () => import('@/views/ProjectPlanning/RequiredDeliverables.vue')

// 执行与监控面板模块
const ExecutionMonitoringOverview = () => import('@/views/ExecutionMonitoring/ExecutionMonitoringOverview.vue')
const HealthStatusDashboard = () => import('@/views/ExecutionMonitoring/HealthStatusDashboard.vue')
const StatusReports = () => import('@/views/ExecutionMonitoring/StatusReports.vue')
const ObstacleManagement = () => import('@/views/ExecutionMonitoring/ObstacleManagement.vue')
const MeetingHub = () => import('@/views/ExecutionMonitoring/MeetingHub.vue')

// 变更管理流程模块
const ChangeManagementOverview = () => import('@/views/ChangeManagement/ChangeManagementOverview.vue')
const ChangeRequest = () => import('@/views/ChangeManagement/ChangeRequest.vue')
const CountdownReminder = () => import('@/views/ChangeManagement/CountdownReminder.vue')

// 知识库与帮助中心模块
const KnowledgeBaseOverview = () => import('@/views/KnowledgeBase/KnowledgeBaseOverview.vue')
const ContextualHelp = () => import('@/views/KnowledgeBase/ContextualHelp.vue')
const LearningCenter = () => import('@/views/KnowledgeBase/LearningCenter.vue')

const routes = [
  {
    path: '/',
    redirect: '/project-kickoff'
  },
  // 项目启动向导路由
  {
    path: '/project-kickoff',
    name: 'ProjectKickoffOverview',
    component: ProjectKickoffOverview,
    meta: { title: '项目启动向导', icon: 'Rocket' }
  },
  {
    path: '/project-kickoff/day-one-checklist',
    name: 'DayOneChecklist',
    component: DayOneChecklist,
    meta: { title: '首日任务清单', icon: 'List', parent: 'ProjectKickoffOverview' }
  },
  {
    path: '/project-kickoff/client-method-discovery',
    name: 'ClientMethodDiscovery',
    component: ClientMethodDiscovery,
    meta: { title: '客户方法发现会议', icon: 'Search', parent: 'ProjectKickoffOverview' }
  },
  // 项目规划中心路由
  {
    path: '/project-planning',
    name: 'ProjectPlanningOverview',
    component: ProjectPlanningOverview,
    meta: { title: '项目规划中心', icon: 'DataBoard' }
  },
  {
    path: '/project-planning/work-plan-builder',
    name: 'WorkPlanBuilder',
    component: WorkPlanBuilder,
    meta: { title: '工作计划构建器', icon: 'Document', parent: 'ProjectPlanningOverview' }
  },
  {
    path: '/project-planning/schedule-release',
    name: 'ScheduleRelease',
    component: ScheduleRelease,
    meta: { title: '计划进度表发布', icon: 'Calendar', parent: 'ProjectPlanningOverview' }
  },
  {
    path: '/project-planning/required-deliverables',
    name: 'RequiredDeliverables',
    component: RequiredDeliverables,
    meta: { title: '必需可交付成果确定', icon: 'Files', parent: 'ProjectPlanningOverview' }
  },
  // 执行与监控面板路由
  {
    path: '/execution-monitoring',
    name: 'ExecutionMonitoringOverview',
    component: ExecutionMonitoringOverview,
    meta: { title: '执行与监控面板', icon: 'Monitor' }
  },
  {
    path: '/execution-monitoring/health-status-dashboard',
    name: 'HealthStatusDashboard',
    component: HealthStatusDashboard,
    meta: { title: '健康状态仪表盘', icon: 'TrendCharts', parent: 'ExecutionMonitoringOverview' }
  },
  {
    path: '/execution-monitoring/status-reports',
    name: 'StatusReports',
    component: StatusReports,
    meta: { title: '每日/每周状态报告', icon: 'Document', parent: 'ExecutionMonitoringOverview' }
  },
  {
    path: '/execution-monitoring/obstacle-management',
    name: 'ObstacleManagement',
    component: ObstacleManagement,
    meta: { title: '障碍管理', icon: 'Warning', parent: 'ExecutionMonitoringOverview' }
  },
  {
    path: '/execution-monitoring/meeting-hub',
    name: 'MeetingHub',
    component: MeetingHub,
    meta: { title: '会议中心', icon: 'ChatRound', parent: 'ExecutionMonitoringOverview' }
  },
  // 变更管理流程路由
  {
    path: '/change-management',
    name: 'ChangeManagementOverview',
    component: ChangeManagementOverview,
    meta: { title: '变更管理流程', icon: 'Switch' }
  },
  {
    path: '/change-management/change-request',
    name: 'ChangeRequest',
    component: ChangeRequest,
    meta: { title: '变更请求', icon: 'Edit', parent: 'ChangeManagementOverview' }
  },
  {
    path: '/change-management/countdown-reminder',
    name: 'CountdownReminder',
    component: CountdownReminder,
    meta: { title: '10天倒计时提醒', icon: 'Timer', parent: 'ChangeManagementOverview' }
  },
  // 知识库与帮助中心路由
  {
    path: '/knowledge-base',
    name: 'KnowledgeBaseOverview',
    component: KnowledgeBaseOverview,
    meta: { title: '知识库与帮助中心', icon: 'Reading' }
  },
  {
    path: '/knowledge-base/contextual-help',
    name: 'ContextualHelp',
    component: ContextualHelp,
    meta: { title: '上下文帮助', icon: 'QuestionFilled', parent: 'KnowledgeBaseOverview' }
  },
  {
    path: '/knowledge-base/learning-center',
    name: 'LearningCenter',
    component: LearningCenter,
    meta: { title: '学习中心', icon: 'School', parent: 'KnowledgeBaseOverview' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
