<template>
  <div class="project-planning-overview">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><DataBoard /></el-icon>
        项目规划中心
      </h1>
      <p class="page-description">
        这是平台的核心，将复杂的规划工作分解为可管理的步骤。
      </p>
    </div>

    <div class="overview-content">
      <div class="module-grid">
        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('work-plan-builder')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><Document /></el-icon>
            </div>
            <h3 class="card-title">工作计划构建器</h3>
            <p class="card-description">
              将复杂的"工作计划三步法"转化为结构化、防错的数字化流程，
              包括信息收集、团队评审、职能经理审核三个步骤。
            </p>
            <div class="card-stats">
              <el-tag type="primary" size="small">三步法</el-tag>
              <el-tag type="success" size="small">向导式</el-tag>
            </div>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('schedule-release')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><Calendar /></el-icon>
            </div>
            <h3 class="card-title">计划进度表发布</h3>
            <p class="card-description">
              通过标准化的"批准-接受"流程，将工作计划转化为项目执行基准，
              包括文件审查、批准会议、验收会议、最终归档四个阶段。
            </p>
            <div class="card-stats">
              <el-tag type="warning" size="small">四阶段</el-tag>
              <el-tag type="info" size="small">强制流程</el-tag>
            </div>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('required-deliverables')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><Files /></el-icon>
            </div>
            <h3 class="card-title">必需可交付成果确定</h3>
            <p class="card-description">
              通过强制性的决策向导，与项目经理合作确定项目需要哪些标准可交付成果，
              避免后期混淆，确保交付范围明确。
            </p>
            <div class="card-stats">
              <el-tag type="danger" size="small">防错设计</el-tag>
              <el-tag type="success" size="small">决策向导</el-tag>
            </div>
          </div>
        </el-card>
      </div>

      <div class="progress-section">
        <h3 class="section-title">规划进度</h3>
        <div class="progress-content">
          <div class="progress-item">
            <div class="progress-label">
              <el-icon><Document /></el-icon>
              <span>工作计划构建</span>
            </div>
            <el-progress 
              :percentage="workPlanProgress" 
              :color="getProgressColor(workPlanProgress)"
              :stroke-width="8"
            />
            <span class="progress-text">{{ getProgressText(workPlanProgress) }}</span>
          </div>
          
          <div class="progress-item">
            <div class="progress-label">
              <el-icon><Calendar /></el-icon>
              <span>进度表发布</span>
            </div>
            <el-progress 
              :percentage="scheduleProgress" 
              :color="getProgressColor(scheduleProgress)"
              :stroke-width="8"
            />
            <span class="progress-text">{{ getProgressText(scheduleProgress) }}</span>
          </div>
          
          <div class="progress-item">
            <div class="progress-label">
              <el-icon><Files /></el-icon>
              <span>可交付成果确定</span>
            </div>
            <el-progress 
              :percentage="deliverablesProgress" 
              :color="getProgressColor(deliverablesProgress)"
              :stroke-width="8"
            />
            <span class="progress-text">{{ getProgressText(deliverablesProgress) }}</span>
          </div>
        </div>
      </div>

      <div class="status-overview">
        <h3 class="section-title">当前状态</h3>
        <div class="status-grid">
          <div class="status-card">
            <div class="status-icon">
              <el-icon size="32" color="#409eff"><TrendCharts /></el-icon>
            </div>
            <div class="status-info">
              <h4>工作计划状态</h4>
              <p class="status-value">{{ workPlanStatus }}</p>
              <p class="status-desc">当前处于{{ getCurrentStepName(workPlanStep) }}</p>
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon">
              <el-icon size="32" color="#67c23a"><SuccessFilled /></el-icon>
            </div>
            <div class="status-info">
              <h4>已完成任务</h4>
              <p class="status-value">{{ completedTasks }}</p>
              <p class="status-desc">共{{ totalTasks }}个规划任务</p>
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon">
              <el-icon size="32" color="#e6a23c"><Clock /></el-icon>
            </div>
            <div class="status-info">
              <h4>待处理事项</h4>
              <p class="status-value">{{ pendingItems }}</p>
              <p class="status-desc">需要您的关注</p>
            </div>
          </div>
        </div>
      </div>

      <div class="quick-actions">
        <h3 class="section-title">快速操作</h3>
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="startWorkPlan">
            <el-icon><Play /></el-icon>
            开始工作计划
          </el-button>
          <el-button type="success" size="large" @click="reviewSchedule">
            <el-icon><View /></el-icon>
            查看进度表
          </el-button>
          <el-button type="warning" size="large" @click="manageDeliverables">
            <el-icon><Setting /></el-icon>
            管理可交付成果
          </el-button>
          <el-button type="info" size="large" @click="viewHelp">
            <el-icon><QuestionFilled /></el-icon>
            查看帮助
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  DataBoard, Document, Calendar, Files, TrendCharts, 
  SuccessFilled, Clock, Play, View, Setting, QuestionFilled 
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const workPlanProgress = ref(0)
const scheduleProgress = ref(0)
const deliverablesProgress = ref(0)
const workPlanStatus = ref('未开始')
const workPlanStep = ref(0)
const completedTasks = ref(0)
const totalTasks = ref(12)
const pendingItems = ref(3)

// 页面挂载时获取数据
onMounted(() => {
  loadOverviewData()
})

// 加载概览数据
const loadOverviewData = () => {
  // 模拟从API获取数据
  setTimeout(() => {
    workPlanProgress.value = 25
    scheduleProgress.value = 0
    deliverablesProgress.value = 0
    workPlanStatus.value = '进行中'
    workPlanStep.value = 1
    completedTasks.value = 3
    pendingItems.value = 2
  }, 500)
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage === 0) return '#dcdfe6'
  if (percentage < 50) return '#e6a23c'
  if (percentage < 100) return '#409eff'
  return '#67c23a'
}

// 获取进度文本
const getProgressText = (percentage) => {
  if (percentage === 0) return '未开始'
  if (percentage < 100) return `${percentage}% 进行中`
  return '已完成'
}

// 获取当前步骤名称
const getCurrentStepName = (step) => {
  const steps = ['信息收集', '团队评审', '职能经理审核', '最终审查']
  return steps[step] || '未开始'
}

// 导航到模块
const navigateToModule = (module) => {
  router.push(`/project-planning/${module}`)
}

// 快速操作方法
const startWorkPlan = () => {
  router.push('/project-planning/work-plan-builder')
}

const reviewSchedule = () => {
  router.push('/project-planning/schedule-release')
}

const manageDeliverables = () => {
  router.push('/project-planning/required-deliverables')
}

const viewHelp = () => {
  ElMessage.info('帮助功能正在开发中...')
}
</script>

<style scoped>
.project-planning-overview {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.overview-content {
  max-width: 1200px;
  margin: 0 auto;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.module-card:hover {
  transform: translateY(-4px);
  border-color: #409eff;
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.card-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.card-stats {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.progress-section, .status-overview, .quick-actions {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.progress-label {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 150px;
  font-weight: 500;
  color: #2c3e50;
}

.progress-text {
  min-width: 100px;
  text-align: right;
  color: #606266;
  font-size: 14px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.status-icon {
  flex-shrink: 0;
}

.status-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin: 0 0 4px 0;
}

.status-desc {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .module-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .progress-label {
    min-width: auto;
  }
  
  .progress-text {
    text-align: center;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
