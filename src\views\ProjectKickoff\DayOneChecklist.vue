<template>
  <div class="day-one-checklist">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><List /></el-icon>
          第一天：启动您的项目
        </h1>
        <div class="progress-indicator">
          <el-progress 
            :percentage="overallProgress" 
            :color="progressColor"
            :stroke-width="12"
            :show-text="false"
          />
          <span class="progress-text">{{ completedTasks }}/{{ totalTasks }} 已完成</span>
        </div>
      </div>
    </div>

    <div class="checklist-content">
      <div class="task-list">
        <div 
          v-for="task in tasks" 
          :key="task.id"
          class="task-card"
          :class="{ 'completed': task.completed, 'expanded': expandedTask === task.id }"
        >
          <div class="task-header" @click="toggleTask(task)">
            <div class="task-checkbox">
              <el-checkbox 
                v-model="task.completed" 
                size="large"
                @change="updateTaskStatus(task)"
              />
            </div>
            <div class="task-info">
              <h3 class="task-title" :class="{ 'completed-text': task.completed }">
                {{ task.title }}
              </h3>
              <p class="task-description">{{ task.description }}</p>
              <div class="task-meta">
                <el-tag 
                  :type="getPriorityType(task.priority)" 
                  size="small"
                >
                  {{ getPriorityText(task.priority) }}
                </el-tag>
                <el-tag type="info" size="small">
                  <el-icon><Clock /></el-icon>
                  {{ task.estimatedTime }}
                </el-tag>
              </div>
            </div>
            <div class="task-actions">
              <el-button 
                v-if="!task.completed"
                type="primary" 
                size="small"
                @click.stop="startTask(task)"
              >
                开始任务
              </el-button>
              <el-button 
                type="info" 
                size="small" 
                circle
                @click.stop="toggleExpand(task.id)"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 任务详细信息展开区域 -->
          <div v-if="expandedTask === task.id" class="task-details">
            <div class="detail-section">
              <h4>任务指导</h4>
              <div v-if="task.id === 1" class="guidance-content">
                <p><strong>会议议程要点：</strong></p>
                <ul>
                  <li>项目必须存在的原因是什么？</li>
                  <li>关键参与者有哪些？</li>
                  <li>项目预期完成日期和预算？</li>
                  <li>项目成功的标准是什么？</li>
                </ul>
                <el-alert 
                  title="重要提醒" 
                  type="warning" 
                  :closable="false"
                  show-icon
                >
                  如果高层管理人员还没有完全确定项目的完工日期和项目预算，那么项目就还没有完全立项。
                </el-alert>
              </div>
              
              <div v-else-if="task.id === 2" class="guidance-content">
                <p><strong>文件夹结构：</strong></p>
                <ul>
                  <li>项目文件 > Outlook模板</li>
                  <li>项目文件 > 项目模板</li>
                  <li>项目文件 > 6可交付成果</li>
                </ul>
                <p><strong>命名规范：</strong>[项目名称]控制日志.xls</p>
              </div>

              <div v-else-if="task.id === 3" class="guidance-content">
                <p><strong>控制日志用途：</strong></p>
                <ul>
                  <li>跟踪工作计划中遗漏的"额外任务"</li>
                  <li>记录项目"障碍"</li>
                  <li>确保100%跟踪项目工作</li>
                </ul>
              </div>

              <div v-else-if="task.id === 4" class="guidance-content">
                <p><strong>会议纪要要求：</strong></p>
                <ul>
                  <li>必须在会议结束后30分钟内编制</li>
                  <li>最晚不得迟于第二天下班时间分发</li>
                  <li>包含讨论要点、决定事项、行动计划</li>
                </ul>
              </div>

              <div v-else-if="task.id === 5" class="guidance-content">
                <p><strong>每日状态报告结构：</strong></p>
                <ul>
                  <li>三个感恩：具体的、积极的事件</li>
                  <li>今天的成就：推动项目的已完成事项</li>
                  <li>明天的计划成就：第二天的具体计划</li>
                </ul>
                <el-alert 
                  title="提交时间" 
                  type="info" 
                  :closable="false"
                  show-icon
                >
                  每日状态报告应在晚上20:00点之前提交
                </el-alert>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="completion-summary" v-if="allTasksCompleted">
        <el-result
          icon="success"
          title="恭喜！首日任务全部完成"
          sub-title="您已成功完成项目启动的关键第一步"
        >
          <template #extra>
            <el-button type="primary" size="large" @click="proceedToNext">
              进入客户方法发现
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  List, Clock, QuestionFilled, ArrowRight 
} from '@element-plus/icons-vue'
import { projectKickoffApi } from '@/api/pmpApi'

const router = useRouter()

// 响应式数据
const tasks = ref([])
const expandedTask = ref(null)

// 计算属性
const totalTasks = computed(() => tasks.value.length)
const completedTasks = computed(() => tasks.value.filter(task => task.completed).length)
const overallProgress = computed(() => {
  if (totalTasks.value === 0) return 0
  return Math.round((completedTasks.value / totalTasks.value) * 100)
})
const progressColor = computed(() => {
  const progress = overallProgress.value
  if (progress < 50) return '#e6a23c'
  if (progress < 100) return '#409eff'
  return '#67c23a'
})
const allTasksCompleted = computed(() => {
  return totalTasks.value > 0 && completedTasks.value === totalTasks.value
})

// 页面挂载时加载数据
onMounted(() => {
  loadTasks()
})

// 加载任务数据
const loadTasks = async () => {
  try {
    const response = await projectKickoffApi.getDayOneChecklist()
    if (response.code === 200) {
      tasks.value = response.data
    }
  } catch (error) {
    ElMessage.error('加载任务数据失败')
  }
}

// 切换任务展开状态
const toggleExpand = (taskId) => {
  expandedTask.value = expandedTask.value === taskId ? null : taskId
}

// 切换任务完成状态
const toggleTask = (task) => {
  if (!task.completed) {
    expandedTask.value = expandedTask.value === task.id ? null : task.id
  }
}

// 更新任务状态
const updateTaskStatus = (task) => {
  if (task.completed) {
    ElMessage.success(`任务"${task.title}"已完成！`)
    // 自动收起详情
    if (expandedTask.value === task.id) {
      expandedTask.value = null
    }
  }
}

// 开始任务
const startTask = (task) => {
  ElMessageBox.confirm(
    `确定要开始执行"${task.title}"吗？`,
    '开始任务',
    {
      confirmButtonText: '开始',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    ElMessage.info(`正在启动"${task.title}"...`)
    // 这里可以添加具体的任务启动逻辑
  }).catch(() => {
    // 用户取消
  })
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return types[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const texts = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return texts[priority] || '普通'
}

// 进入下一步
const proceedToNext = () => {
  router.push('/project-kickoff/client-method-discovery')
}
</script>

<style scoped>
.day-one-checklist {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 200px;
}

.progress-text {
  font-weight: 500;
  color: #2c3e50;
  white-space: nowrap;
}

.checklist-content {
  max-width: 1000px;
  margin: 0 auto;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.task-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.task-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.task-card.completed {
  background-color: #f0f9ff;
  border-left: 4px solid #67c23a;
}

.task-header {
  display: flex;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  gap: 16px;
}

.task-checkbox {
  flex-shrink: 0;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  transition: all 0.3s ease;
}

.task-title.completed-text {
  color: #67c23a;
  text-decoration: line-through;
}

.task-description {
  color: #606266;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.task-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.task-details {
  border-top: 1px solid #ebeef5;
  padding: 20px;
  background-color: #fafbfc;
}

.detail-section h4 {
  color: #2c3e50;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.guidance-content ul {
  margin: 12px 0;
  padding-left: 20px;
}

.guidance-content li {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}

.guidance-content p {
  margin: 12px 0;
  color: #2c3e50;
}

.completion-summary {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .progress-indicator {
    min-width: auto;
  }
  
  .task-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .task-actions {
    justify-content: center;
  }
}
</style>
