// PMP ProGuide平台模拟API数据层

// 统一响应格式
const createResponse = (data, msg = '操作成功', code = 200) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code,
        msg,
        data
      });
    }, 100); // 模拟网络延迟
  });
};

// 项目启动向导相关API
export const projectKickoffApi = {
  // 获取首日任务清单
  getDayOneChecklist() {
    const mockData = [
      {
        id: 1,
        title: '采访项目负责人',
        description: '与项目负责人进行首次会面，收集项目基础信息',
        completed: false,
        priority: 'high',
        estimatedTime: '2小时'
      },
      {
        id: 2,
        title: '创建项目电子文件夹',
        description: '在云端创建完整的项目文件夹结构',
        completed: false,
        priority: 'high',
        estimatedTime: '30分钟'
      },
      {
        id: 3,
        title: '建立控制日志',
        description: '创建并初始化项目控制日志模板',
        completed: false,
        priority: 'medium',
        estimatedTime: '20分钟'
      },
      {
        id: 4,
        title: '完成会议纪要',
        description: '编制并分发项目启动会议纪要',
        completed: false,
        priority: 'high',
        estimatedTime: '45分钟'
      },
      {
        id: 5,
        title: '发布每日状态报告',
        description: '提交第一份每日状态报告',
        completed: false,
        priority: 'medium',
        estimatedTime: '15分钟'
      }
    ];
    return createResponse(mockData);
  },

  // 获取客户方法发现会议数据
  getClientMethodDiscovery() {
    const mockData = {
      standardDeliverables: [
        { id: 1, name: '项目章程', selected: false, purpose: '' },
        { id: 2, name: '工作计划', selected: true, purpose: '明确项目任务和时间安排' },
        { id: 3, name: '状态报告', selected: true, purpose: '定期汇报项目进展' },
        { id: 4, name: '预算报告', selected: false, purpose: '' },
        { id: 5, name: 'A&A表格', selected: true, purpose: '记录批准和接受信息' }
      ],
      meetingStatus: 'pending',
      documentsUploaded: [],
      templatesRequired: []
    };
    return createResponse(mockData);
  }
};

// 项目规划中心相关API
export const projectPlanningApi = {
  // 获取工作计划构建器数据
  getWorkPlanBuilder() {
    const mockData = {
      currentStep: 1,
      steps: [
        { id: 1, name: '收集信息', status: 'active', completed: false },
        { id: 2, name: '团队评审', status: 'pending', completed: false },
        { id: 3, name: '职能经理审核', status: 'pending', completed: false },
        { id: 4, name: '最终审查', status: 'pending', completed: false }
      ],
      tasks: [
        {
          id: 1,
          name: '需求分析',
          workHours: 16,
          dependencies: [],
          assignee: '张三',
          status: 'pending'
        },
        {
          id: 2,
          name: '系统设计',
          workHours: 24,
          dependencies: [1],
          assignee: '李四',
          status: 'pending'
        }
      ]
    };
    return createResponse(mockData);
  },

  // 获取计划进度表发布数据
  getScheduleRelease() {
    const mockData = {
      currentPhase: 1,
      phases: [
        { id: 1, name: '文件审查', status: 'active', approver: '项目经理' },
        { id: 2, name: '批准会议', status: 'pending', approver: '项目负责人' },
        { id: 3, name: '验收会议', status: 'pending', approver: '团队成员' },
        { id: 4, name: '最终归档', status: 'pending', approver: '系统' }
      ],
      documents: [
        { name: '工作计划', status: 'ready', version: 'v1.0' },
        { name: 'WBS', status: 'generating', version: 'v1.0' },
        { name: '角色矩阵', status: 'ready', version: 'v1.0' },
        { name: '沟通计划', status: 'ready', version: 'v1.0' }
      ]
    };
    return createResponse(mockData);
  },

  // 获取必需可交付成果数据
  getRequiredDeliverables() {
    const mockData = {
      standardDeliverables: [
        { id: 1, name: '项目章程', required: true, excluded: false, reason: '' },
        { id: 2, name: '工作计划', required: true, excluded: false, reason: '' },
        { id: 3, name: '状态报告', required: true, excluded: false, reason: '' },
        { id: 4, name: '预算报告', required: false, excluded: true, reason: '客户不需要详细预算跟踪' }
      ],
      customDeliverables: [
        { id: 1, name: '安全合规报告', description: '客户特定要求的安全文档' }
      ],
      approvalStatus: 'pending'
    };
    return createResponse(mockData);
  }
};

// 执行与监控面板相关API
export const executionMonitoringApi = {
  // 获取健康状态仪表盘数据
  getHealthStatusDashboard() {
    const mockData = {
      projectHealth: 'yellow', // green, yellow, red
      overallProgress: {
        planned: 65,
        actual: 58
      },
      criticalTasks: [
        {
          id: 1,
          name: '系统集成测试',
          health: 'red',
          timeFloat: 3,
          delayDays: 2,
          assignee: '王五'
        },
        {
          id: 2,
          name: '用户培训准备',
          health: 'yellow',
          timeFloat: 8,
          delayDays: 0,
          assignee: '赵六'
        }
      ],
      obstacles: [
        {
          id: 1,
          name: '服务器硬件延期交付',
          delayDate: '2024-08-15',
          health: 'red',
          assignee: '张三',
          daysRemaining: 5
        }
      ]
    };
    return createResponse(mockData);
  },

  // 获取状态报告数据
  getStatusReports() {
    const mockData = {
      dailyReports: [
        {
          id: 1,
          date: '2024-08-01',
          gratitudes: [
            '感谢团队成员在技术评审会议上的积极参与',
            '感谢客户及时提供了需求澄清',
            '感谢测试团队发现的关键问题'
          ],
          achievements: [
            '完成了系统架构设计评审',
            '解决了数据库连接问题',
            '更新了项目风险登记册'
          ],
          tomorrowPlans: [
            '开始用户界面开发',
            '安排与客户的需求确认会议',
            '完成单元测试用例编写'
          ]
        }
      ],
      weeklyReports: [
        {
          id: 1,
          weekEnding: '2024-08-02',
          projectHealth: 'yellow',
          completionPercentage: 58,
          statusSummary: '项目整体进展良好，但存在硬件交付延期风险...',
          recentAchievements: [
            '完成需求分析文档',
            '通过技术架构评审',
            '建立开发环境'
          ],
          plannedAchievements: [
            '完成核心模块开发',
            '开始系统集成测试',
            '准备用户培训材料'
          ]
        }
      ]
    };
    return createResponse(mockData);
  },

  // 获取障碍管理数据
  getObstacleManagement() {
    const mockData = {
      obstacles: [
        {
          id: 1,
          name: '服务器硬件延期交付',
          status: 'imminent', // pending, imminent, assessed, approved, resolved
          delayDate: '2024-08-15',
          assignee: '张三',
          health: 'red',
          createdDate: '2024-07-25',
          daysRemaining: 5
        },
        {
          id: 2,
          name: '第三方API接口变更',
          status: 'assessed',
          delayDate: '2024-08-20',
          assignee: '李四',
          health: 'yellow',
          createdDate: '2024-07-28',
          daysRemaining: 10
        }
      ]
    };
    return createResponse(mockData);
  },

  // 获取会议中心数据
  getMeetingHub() {
    const mockData = {
      upcomingMeetings: [
        {
          id: 1,
          title: '项目状态评审会议',
          type: 'status-review',
          date: '2024-08-05',
          time: '14:00',
          duration: 60,
          attendees: ['张三', '李四', '王五'],
          status: 'scheduled'
        },
        {
          id: 2,
          title: '技术架构讨论',
          type: 'technical-review',
          date: '2024-08-06',
          time: '10:00',
          duration: 90,
          attendees: ['李四', '赵六'],
          status: 'scheduled'
        }
      ],
      meetingTemplates: [
        { id: 1, name: '项目启动会议', duration: 120 },
        { id: 2, name: '每周状态评审', duration: 60 },
        { id: 3, name: '技术评审会议', duration: 90 },
        { id: 4, name: '客户演示会议', duration: 60 }
      ]
    };
    return createResponse(mockData);
  }
};

// 变更管理流程相关API
export const changeManagementApi = {
  // 获取变更请求数据
  getChangeRequests() {
    const mockData = {
      activeRequests: [
        {
          id: 1,
          title: '增加移动端支持功能',
          type: 'client-request',
          status: 'assessment', // request, assessment, approval, acceptance
          initiator: '客户代表',
          createdDate: '2024-07-30',
          dueDate: '2024-08-09',
          daysRemaining: 8,
          priority: 'high'
        }
      ],
      completedRequests: [
        {
          id: 2,
          title: '数据库性能优化',
          type: 'obstacle-driven',
          status: 'completed',
          initiator: '技术团队',
          createdDate: '2024-07-15',
          completedDate: '2024-07-25'
        }
      ]
    };
    return createResponse(mockData);
  },

  // 获取倒计时提醒数据
  getCountdownReminders() {
    const mockData = {
      activeReminders: [
        {
          id: 1,
          title: '项目变更评估',
          type: 'change-assessment',
          dueDate: '2024-08-09',
          daysRemaining: 8,
          priority: 'critical',
          relatedItem: '增加移动端支持功能'
        },
        {
          id: 2,
          title: '障碍评估',
          type: 'obstacle-assessment',
          dueDate: '2024-08-07',
          daysRemaining: 6,
          priority: 'high',
          relatedItem: '服务器硬件延期交付'
        }
      ]
    };
    return createResponse(mockData);
  }
};

// 知识库与帮助中心相关API
export const knowledgeBaseApi = {
  // 获取上下文帮助数据
  getContextualHelp(context) {
    const mockData = {
      title: '工作计划步骤1: 收集信息',
      principles: [
        '项目管理者不得在客户在场的情况下，在工作计划中输入或修改任务'
      ],
      criteria: [
        '此步骤的目标是收集信息，而非创建计划',
        '请勿在会议中打开MS Project'
      ],
      keyPoints: [
        '如果供应商也需要承担项目的任务，则在工作计划步骤1的会议中，邀请供应商团队成员出席非常重要'
      ],
      actionGuide: [
        '确定所有任务',
        '确定工作量(小时)',
        '确定前/后接续关系',
        '确定团队成员分配',
        '确定外部依存关系'
      ]
    };
    return createResponse(mockData);
  },

  // 获取学习中心数据
  getLearningCenter() {
    const mockData = {
      interactiveGuides: [
        {
          id: 1,
          title: '如何完成客户方法发现会议',
          description: '模拟真实的会议流程，学习收集客户需求',
          duration: '15分钟',
          difficulty: 'beginner',
          completed: false
        },
        {
          id: 2,
          title: '如何发布每周项目状态报告',
          description: '学习状态报告的编制和发布流程',
          duration: '20分钟',
          difficulty: 'intermediate',
          completed: true
        }
      ],
      shortVideos: [
        {
          id: 1,
          title: '什么是延迟日期？',
          description: '动画演示延迟日期的计算方法',
          duration: '3分钟',
          views: 1250
        },
        {
          id: 2,
          title: '项目健康状态的三种颜色',
          description: '解释绿色、黄色、红色的含义和应对策略',
          duration: '5分钟',
          views: 890
        }
      ],
      knowledgeCards: [
        {
          id: 1,
          title: '工作计划三步法速查表',
          description: '快速查看三个步骤的要点',
          category: 'planning'
        },
        {
          id: 2,
          title: '常见会议议程一览',
          description: '所有会议类型的议程模板',
          category: 'meetings'
        }
      ]
    };
    return createResponse(mockData);
  }
};

// 导出所有API
export default {
  projectKickoffApi,
  projectPlanningApi,
  executionMonitoringApi,
  changeManagementApi,
  knowledgeBaseApi
};
