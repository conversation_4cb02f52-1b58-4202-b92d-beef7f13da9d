<template>
  <div class="health-status-dashboard">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><TrendCharts /></el-icon>
        健康状态仪表盘
      </h1>
      <p class="page-description">
        项目运行时的"驾驶舱"，实时、自动、可视化的决策支持中心。
      </p>
    </div>

    <div class="dashboard-content">
      <!-- 中心健康状态指示器 -->
      <div class="center-health">
        <el-card class="health-indicator-card">
          <div class="health-display">
            <div class="health-light" :class="projectHealth.status">
              <div class="light-inner"></div>
            </div>
            <div class="health-info">
              <h2>项目健康状态</h2>
              <p class="health-status">{{ getHealthText(projectHealth.status) }}</p>
              <p class="health-reason">{{ projectHealth.reason }}</p>
            </div>
          </div>
          
          <div class="expert-advice">
            <el-alert 
              title="专家建议" 
              type="info" 
              :closable="false"
              show-icon
            >
              敢于将项目标记为黄色或红色，是获取资源、避免项目失败的正确做法。这应被奖励，而非惩罚。
            </el-alert>
          </div>
        </el-card>
      </div>

      <!-- 四个核心视图 -->
      <div class="dashboard-views">
        <!-- 项目总览 -->
        <el-card class="view-card">
          <template #header>
            <h3>项目总览</h3>
          </template>
          
          <div class="project-overview">
            <div class="progress-comparison">
              <div class="progress-item">
                <span class="progress-label">计划进度</span>
                <el-progress 
                  :percentage="overallProgress.planned" 
                  color="#409eff"
                  :stroke-width="8"
                />
                <span class="progress-value">{{ overallProgress.planned }}%</span>
              </div>
              <div class="progress-item">
                <span class="progress-label">实际进度</span>
                <el-progress 
                  :percentage="overallProgress.actual" 
                  :color="getProgressColor(overallProgress.actual, overallProgress.planned)"
                  :stroke-width="8"
                />
                <span class="progress-value">{{ overallProgress.actual }}%</span>
              </div>
            </div>
            
            <div class="progress-analysis" v-if="overallProgress.actual < overallProgress.planned">
              <el-alert 
                title="进度分析" 
                type="warning" 
                :closable="false"
                show-icon
              >
                实际进度落后于计划进度 {{ overallProgress.planned - overallProgress.actual }}%，
                需要关注关键路径任务的执行情况。
              </el-alert>
            </div>
          </div>
        </el-card>

        <!-- 任务健康 -->
        <el-card class="view-card">
          <template #header>
            <h3>任务健康</h3>
          </template>
          
          <div class="task-health">
            <div class="health-summary">
              <div class="summary-item">
                <el-icon size="20" color="#67c23a"><SuccessFilled /></el-icon>
                <span>正常：{{ healthSummary.green }}个</span>
              </div>
              <div class="summary-item">
                <el-icon size="20" color="#e6a23c"><WarningFilled /></el-icon>
                <span>延迟：{{ healthSummary.yellow }}个</span>
              </div>
              <div class="summary-item">
                <el-icon size="20" color="#f56c6c"><CircleCloseFilled /></el-icon>
                <span>延期：{{ healthSummary.red }}个</span>
              </div>
            </div>
            
            <div class="critical-tasks">
              <h4>关键路径任务 (时间裕量 ≤ 10天)</h4>
              <div class="task-list">
                <div 
                  v-for="task in criticalTasks" 
                  :key="task.id"
                  class="task-item"
                  :class="task.health"
                >
                  <div class="task-info">
                    <h5>{{ task.name }}</h5>
                    <p>负责人：{{ task.assignee }} | 时间裕量：{{ task.timeFloat }}天</p>
                  </div>
                  <el-tag 
                    :type="getTaskHealthType(task.health)" 
                    size="small"
                  >
                    {{ getTaskHealthText(task.health) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 障碍优先级 -->
        <el-card class="view-card">
          <template #header>
            <h3>障碍优先级</h3>
          </template>
          
          <div class="obstacle-priority">
            <div class="priority-explanation">
              <p>按"延迟日期"自动排序，优先处理延迟日期最早的障碍。</p>
            </div>
            
            <div class="obstacle-list">
              <div 
                v-for="obstacle in obstacles" 
                :key="obstacle.id"
                class="obstacle-item"
                :class="{ 'imminent': obstacle.daysRemaining <= 10 }"
              >
                <div class="obstacle-info">
                  <h5>{{ obstacle.name }}</h5>
                  <p>延迟日期：{{ obstacle.delayDate }} | 负责人：{{ obstacle.assignee }}</p>
                </div>
                <div class="obstacle-status">
                  <el-tag 
                    :type="obstacle.daysRemaining <= 10 ? 'danger' : 'warning'" 
                    size="small"
                  >
                    {{ obstacle.daysRemaining }}天后延迟
                  </el-tag>
                  <el-tag 
                    :type="getTaskHealthType(obstacle.health)" 
                    size="small"
                  >
                    {{ getTaskHealthText(obstacle.health) }}
                  </el-tag>
                </div>
                <div class="obstacle-actions" v-if="obstacle.daysRemaining <= 10">
                  <el-button type="danger" size="small" @click="startObstacleAssessment(obstacle)">
                    启动评估
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 关键路径 -->
        <el-card class="view-card">
          <template #header>
            <h3>关键路径</h3>
          </template>
          
          <div class="critical-path">
            <div class="path-explanation">
              <p>显示时间裕量 = 0 的任务链，这些任务的延迟将直接影响项目完成日期。</p>
            </div>
            
            <div class="path-visualization">
              <div class="path-timeline">
                <div 
                  v-for="(task, index) in criticalPathTasks" 
                  :key="task.id"
                  class="timeline-item"
                >
                  <div class="timeline-node" :class="task.status">
                    <span>{{ index + 1 }}</span>
                  </div>
                  <div class="timeline-content">
                    <h5>{{ task.name }}</h5>
                    <p>{{ task.duration }}天 | {{ task.assignee }}</p>
                  </div>
                  <div v-if="index < criticalPathTasks.length - 1" class="timeline-connector"></div>
                </div>
              </div>
            </div>
            
            <div class="path-simulation">
              <h4>影响模拟</h4>
              <p>如果关键路径上的任务延迟1天，项目完成日期将推迟1天。</p>
              <el-button type="primary" size="small" @click="simulateDelay">
                模拟延迟影响
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 实时数据更新提示 -->
      <div class="data-update">
        <el-card>
          <div class="update-info">
            <el-icon><Refresh /></el-icon>
            <span>数据实时更新 | 最后更新：{{ lastUpdateTime }}</span>
            <el-button type="text" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  TrendCharts, SuccessFilled, WarningFilled, CircleCloseFilled, Refresh 
} from '@element-plus/icons-vue'
import { executionMonitoringApi } from '@/api/pmpApi'

const router = useRouter()

// 响应式数据
const projectHealth = ref({
  status: 'yellow',
  reason: '存在时间裕量少于10天的黄色任务，需要关注'
})
const overallProgress = ref({
  planned: 65,
  actual: 58
})
const healthSummary = ref({
  green: 12,
  yellow: 3,
  red: 1
})
const criticalTasks = ref([])
const obstacles = ref([])
const criticalPathTasks = ref([
  { id: 1, name: '需求分析', duration: 5, assignee: '张三', status: 'completed' },
  { id: 2, name: '系统设计', duration: 8, assignee: '李四', status: 'in-progress' },
  { id: 3, name: '开发实现', duration: 15, assignee: '王五', status: 'pending' },
  { id: 4, name: '系统测试', duration: 10, assignee: '赵六', status: 'pending' },
  { id: 5, name: '部署上线', duration: 3, assignee: '张三', status: 'pending' }
])
const lastUpdateTime = ref('')

// 页面挂载时加载数据
onMounted(() => {
  loadDashboardData()
  updateLastUpdateTime()
})

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    const response = await executionMonitoringApi.getHealthStatusDashboard()
    if (response.code === 200) {
      const data = response.data
      projectHealth.value.status = data.projectHealth
      overallProgress.value = data.overallProgress
      criticalTasks.value = data.criticalTasks
      obstacles.value = data.obstacles
    }
  } catch (error) {
    ElMessage.error('加载仪表盘数据失败')
  }
}

// 更新最后更新时间
const updateLastUpdateTime = () => {
  const now = new Date()
  lastUpdateTime.value = now.toLocaleTimeString()
}

// 获取健康状态文本
const getHealthText = (status) => {
  const texts = {
    green: '健康',
    yellow: '注意',
    red: '危险'
  }
  return texts[status] || '未知'
}

// 获取进度条颜色
const getProgressColor = (actual, planned) => {
  if (actual >= planned) return '#67c23a'
  if (actual >= planned - 10) return '#e6a23c'
  return '#f56c6c'
}

// 获取任务健康状态类型
const getTaskHealthType = (health) => {
  const types = {
    green: 'success',
    yellow: 'warning',
    red: 'danger'
  }
  return types[health] || 'info'
}

// 获取任务健康状态文本
const getTaskHealthText = (health) => {
  const texts = {
    green: '正常',
    yellow: '延迟',
    red: '延期'
  }
  return texts[health] || '未知'
}

// 启动障碍评估
const startObstacleAssessment = (obstacle) => {
  ElMessage.info(`正在启动障碍"${obstacle.name}"的评估流程...`)
  router.push('/execution-monitoring/obstacle-management')
}

// 模拟延迟影响
const simulateDelay = () => {
  ElMessage.info('模拟结果：如果关键路径任务延迟1天，项目完成日期将推迟1天')
}

// 刷新数据
const refreshData = () => {
  loadDashboardData()
  updateLastUpdateTime()
  ElMessage.success('数据已刷新')
}
</script>

<style scoped>
.health-status-dashboard {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
}

.center-health {
  margin-bottom: 32px;
}

.health-indicator-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.health-display {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.health-light {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.health-light.green {
  background-color: #67c23a;
  box-shadow: 0 0 30px rgba(103, 194, 58, 0.6);
}

.health-light.yellow {
  background-color: #e6a23c;
  box-shadow: 0 0 30px rgba(230, 162, 60, 0.6);
}

.health-light.red {
  background-color: #f56c6c;
  box-shadow: 0 0 30px rgba(245, 108, 108, 0.6);
}

.light-inner {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.health-info h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
}

.health-status {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.health-reason {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.dashboard-views {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.view-card {
  height: fit-content;
}

.progress-comparison {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-label {
  min-width: 80px;
  font-weight: 500;
  color: #2c3e50;
}

.progress-value {
  min-width: 40px;
  text-align: right;
  font-weight: 600;
  color: #2c3e50;
}

.health-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.critical-tasks h4 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 16px;
}

.task-list, .obstacle-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item, .obstacle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #dcdfe6;
}

.task-item.red, .obstacle-item.imminent {
  border-left-color: #f56c6c;
  background-color: #fef0f0;
}

.task-item.yellow {
  border-left-color: #e6a23c;
  background-color: #fdf6ec;
}

.task-item.green {
  border-left-color: #67c23a;
  background-color: #f0f9ff;
}

.task-info h5, .obstacle-info h5 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 14px;
}

.task-info p, .obstacle-info p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.obstacle-status {
  display: flex;
  gap: 8px;
}

.priority-explanation, .path-explanation {
  color: #606266;
  margin-bottom: 16px;
  font-size: 14px;
}

.path-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.timeline-node {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 12px;
}

.timeline-node.completed {
  background-color: #67c23a;
}

.timeline-node.in-progress {
  background-color: #409eff;
}

.timeline-node.pending {
  background-color: #dcdfe6;
  color: #606266;
}

.timeline-content h5 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 14px;
}

.timeline-content p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.timeline-connector {
  position: absolute;
  left: 15px;
  top: 32px;
  width: 2px;
  height: 16px;
  background-color: #dcdfe6;
}

.path-simulation {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.path-simulation h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 14px;
}

.path-simulation p {
  color: #606266;
  margin-bottom: 12px;
  font-size: 14px;
}

.data-update {
  margin-bottom: 24px;
}

.update-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #606266;
  font-size: 14px;
}

@media (max-width: 768px) {
  .health-display {
    flex-direction: column;
    text-align: center;
  }
  
  .dashboard-views {
    grid-template-columns: 1fr;
  }
  
  .progress-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .health-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .task-item, .obstacle-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
</style>
