<template>
  <div class="obstacle-management">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Warning /></el-icon>
        障碍管理
      </h1>
      <p class="page-description">
        "危机处理中心"，将复杂的障碍管理流程转化为强制性、防错的闭环工作流。
      </p>
    </div>

    <div class="management-content">
      <div class="obstacle-list">
        <el-card class="list-card">
          <template #header>
            <div class="card-header">
              <h3>障碍列表</h3>
              <el-button type="primary" @click="addObstacle">
                <el-icon><Plus /></el-icon>
                添加障碍
              </el-button>
            </div>
          </template>
          
          <div class="obstacles">
            <div 
              v-for="obstacle in obstacles" 
              :key="obstacle.id"
              class="obstacle-item"
              :class="{ 'imminent': obstacle.daysRemaining <= 10 }"
            >
              <div class="obstacle-info">
                <h4>{{ obstacle.name }}</h4>
                <p>延迟日期：{{ obstacle.delayDate }} | 负责人：{{ obstacle.assignee }}</p>
                <p>创建日期：{{ obstacle.createdDate }}</p>
              </div>
              <div class="obstacle-status">
                <el-tag 
                  :type="getStatusType(obstacle.status)" 
                  size="small"
                >
                  {{ getStatusText(obstacle.status) }}
                </el-tag>
                <el-tag 
                  :type="obstacle.daysRemaining <= 10 ? 'danger' : 'warning'" 
                  size="small"
                >
                  {{ obstacle.daysRemaining }}天后延迟
                </el-tag>
              </div>
              <div class="obstacle-actions">
                <el-button 
                  v-if="obstacle.status === 'pending'"
                  type="warning" 
                  size="small"
                  @click="startAssessment(obstacle)"
                >
                  启动评估
                </el-button>
                <el-button 
                  v-if="obstacle.status === 'assessed'"
                  type="success" 
                  size="small"
                  @click="requestApproval(obstacle)"
                >
                  请求批准
                </el-button>
                <el-button 
                  type="info" 
                  size="small"
                  @click="viewDetails(obstacle)"
                >
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning, Plus } from '@element-plus/icons-vue'
import { executionMonitoringApi } from '@/api/pmpApi'

// 响应式数据
const obstacles = ref([])

// 页面挂载时加载数据
onMounted(() => {
  loadObstacles()
})

// 加载障碍数据
const loadObstacles = async () => {
  try {
    const response = await executionMonitoringApi.getObstacleManagement()
    if (response.code === 200) {
      obstacles.value = response.data.obstacles
    }
  } catch (error) {
    ElMessage.error('加载障碍数据失败')
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'info',
    imminent: 'warning',
    assessed: 'primary',
    approved: 'success',
    resolved: 'success'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    imminent: '迫在眉睫',
    assessed: '已评估',
    approved: '已批准',
    resolved: '已解决'
  }
  return texts[status] || '未知'
}

// 添加障碍
const addObstacle = () => {
  ElMessage.info('添加障碍功能正在开发中...')
}

// 启动评估
const startAssessment = (obstacle) => {
  ElMessage.info(`正在启动障碍"${obstacle.name}"的评估流程...`)
}

// 请求批准
const requestApproval = (obstacle) => {
  ElMessage.info(`正在请求批准障碍"${obstacle.name}"的解决方案...`)
}

// 查看详情
const viewDetails = (obstacle) => {
  ElMessage.info(`查看障碍"${obstacle.name}"的详细信息...`)
}
</script>

<style scoped>
.obstacle-management {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.management-content {
  max-width: 1000px;
  margin: 0 auto;
}

.list-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.obstacles {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.obstacle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #dcdfe6;
}

.obstacle-item.imminent {
  border-left-color: #f56c6c;
  background-color: #fef0f0;
}

.obstacle-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
}

.obstacle-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.obstacle-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.obstacle-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media (max-width: 768px) {
  .obstacle-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .obstacle-status {
    flex-direction: row;
    justify-content: center;
  }
  
  .obstacle-actions {
    flex-direction: row;
    justify-content: center;
  }
}
</style>
