#系统名称：PMP ProGuide平台

##PMP ProGuide平台设计理念

- 向导式流程 (Wizard-Driven): 将整个项目生命周期（启动、规划、执行、监控、收尾）分解为一系列清晰的、按时间顺序排列的“任务步骤”。用户只需跟着“下一步”按钮走，平台会自动引导他们完成每个环节。
- 模板化与自动化 (Template & Automation): 内置所有文档模板（章程、工作计划、A&A表、状态报告等），并根据预设规则（如文件命名规范）自动填充和保存，消除用户手动操作的繁琐和错误。
- 智能检查与提醒 (Intelligent Check & Reminders): 在每个关键节点，平台会自动检查用户的输入是否符合标准（如健康状态、文件命名、必填项），并提供实时反馈和提醒。
- 集成化协作 (Integrated Collaboration): 将会议安排、文档审阅、审批、沟通等环节深度集成，用户无需切换多个工具（如Outlook、Word、Excel）。
- 知识库内嵌 (Embedded Knowledge Base): 将文件中的“评判标准”、“设计目标”、“要点精炼”等关键知识，作为上下文帮助信息，直接嵌入到相关操作界面中。
##PMP ProGuide 平台核心功能模块

###模块一：项目启动向导 (Project Kickoff Wizard)

这是项目管理小白的“新手村”，引导用户完成项目最初的关键几步。

####功能1.1：首日任务清单 (Day 1 Checklist)

此功能是项目管理新手的“第一步”，必须严格遵循文档中“第三章 第一天的管理操作指南”和“第一周的项目管理指南”的具体步骤。

- 界面: 一个清晰的、按时间顺序排列的待办事项列表，带有进度条和上下文帮助提示。
- 内容与交互:
    * [ ] 采访项目负责人 (Interview the Project Sponsor)
        + 引导: 点击此项，平台弹出“项目启动前与项目负责人的会面”议程设置（来自文档P7）。
        + 内容: 界面显示完整的议程表格（目标、参与者、技术要求、标准可交付成果等），用户需在每个议程项下输入收集到的信息。平台会提供关键问题的提示，如：“项目必须存在的原因是什么？”、“关键参与者有哪些？”、“项目预期完成日期和预算？”。
        + 输出: 信息输入完毕后，系统自动生成“客户方法发现会议”的会议邀请草稿和“每日状态报告”的草稿。
        + 关键点: 平台会强制要求用户填写“项目预期完成日期”和“项目预算”字段，否则无法标记此任务为完成，因为文档强调“如果位于食物链顶端的高层管理人员还没有完全确定项目的完工日期和项目预算，那么项目就还没有完全立项。”
    * [ ] 创建项目电子文件夹 (Create Project Electronic Folder)
        + 自动化: 用户点击复选框后，平台立即在云端（如SharePoint或OneDrive）为该项目创建完整的文件夹结构。
        + 结构: 严格遵循文档要求，创建 项目文件 > Outlook模板, 项目模板, 6可交付成果 等子文件夹。
        + 命名: 自动应用文件命名惯例（如 [项目名称]控制日志.xls）。平台会提示用户输入项目全称，以确保命名准确。
        + 验证: 创建完成后，平台会显示文件夹结构的预览图，供用户确认。
    * [ ] 建立控制日志 (Establish Control Log)
        + 自动化: 点击后，平台自动从“项目模板”文件夹中调用“控制日志”模板。
        + 预填充: 自动将项目名称填入文件标题和表头。
        + 保存: 自动将文件保存到刚创建的项目电子文件夹根目录下，文件名为 [项目名称]控制日志.xls。
        + 引导: 提示用户，控制日志用于跟踪工作计划中遗漏的“额外任务”和“障碍”，是确保100%跟踪项目工作的关键工具。
    * [ ] 完成会议纪要 (Complete Meeting Minutes)
        + 引导: 点击此项，平台打开一个基于“项目启动前与项目负责人的会面”的预设会议纪要模板。
        + 内容: 纪要模板的“讨论要点”部分已根据用户在“采访项目负责人”环节输入的信息进行初步填充。
        + 交互: 用户只需进行最后的校对和微调。
        + 关键点: 平台会提供“要点精炼”提示：“会议纪要应在会议结束后的30分钟内编制和分发，并且最晚不得迟于第二天下班时间。”
    * [ ] 发布每日状态报告 (Publish Daily Status Report)
        + 集成: 点击此项，平台跳转到“发布每日状态报告”模块。
        + 预填充:
            - 三个感恩: 提示用户输入三项具体的、能提升幸福感的感恩事项。
            - 今天的成就: 自动包含前四项任务的完成情况（如：“已完成与项目负责人的首次面谈”），并引导用户将“参加会议”转化为“会议中取得的成就”。
            - 明天的计划成就: 提示用户列出第二天的计划，特别是安排“客户方法发现会议”。
        + 规则提醒: 平台会明确提示：“每日状态报告应在晚上20:00点之前提交”。
        + 交互: 用户确认内容后，一键发布，平台自动将报告的PDF版本存档。


####功能1.2：客户方法发现会议 (Client Method Discovery Meeting)

此功能旨在将文档中“安排客户方法发现会议的议程”（P5-P6）的要求，转化为一个强制性的、防错的数字化工作流，确保项目管理小白也能完整、准确地收集到项目规划所必需的基础信息。

- 界面: 一个分步骤的、带有进度指示的表单，完全对应会议议程的五个核心环节。
- 内容与流程:
    * [ ] 确定客户方法要求的所有标准可交付成果
        + 输入: 提供一个可勾选的列表，列出文档中提到的常见标准可交付成果（如项目章程、工作计划、状态报告、预算报告等）。用户必须勾选或添加自定义项。
        + 交互: 平台会提示用户：“这是完成计划进度表的前提。请务必在项目分配后的第二周结束前完成此发现。”
    * [ ] 确定每个标准可交付成果的目的/价值
        + 输入: 对于上一步选中的每个可交付成果，提供一个强制填写的文本框，要求用户输入其目的和价值。
        + 引导: 提示语：“请说明这个交付物对项目成功有何帮助？例如，‘项目章程’的目的是‘明确项目范围，获得高层授权’。”
    * [ ] 获取与每个标准可交付成果相关的任何说明/文档
        + 输入: 提供一个文件上传区域，用户可上传客户的内部方法论文档、政策文件或任何相关的说明性材料。
        + 验证: 如果用户未上传任何文件，平台会弹出提醒：“是否已获取所有相关说明文档？若无，请确认。”
    * [ ] 获取与标准交付物一起使用的所有必需模板
        + 输入: 提供一个文件上传区域，用于上传客户要求的、必须使用的模板文件（如客户特定的Word、Excel模板）。
        + 关键点: 平台强调：“项目管理者不得在客户在场的情况下，在工作计划中输入或修改任务。” 因此，此步骤的目的是收集信息，而非现场创建。
    * [ ] 了解模板每个栏中所需的内容
        + 输入: 一个文本框，用于记录用户对模板字段的理解和备注。例如，“‘预算编号’栏需要填写客户ERP系统中的项目编码。”
- 高级功能与集成:
    * 会议安排自动化: 完成表单后，平台提供“一键创建会议”按钮。点击后，自动调用“Outlook模板”中的“客户方法发现会议”模板，预填充主题 [项目名称]:客户方法发现会议，并引导用户添加参会人（方法决策者/领域专家）和会议地点。
    * 会议纪要生成与发布:
        + 会议结束后，用户点击“生成纪要”，平台将表单中的所有内容，按照标准格式（来自文档P30的会议纪要示例）自动填充到会议纪要模板中。
        + 强制规则: 平台内置规则：“会议纪要必须在会议结束后的30分钟内发布，最晚不得迟于第二天下班时间。” 如果用户尝试在截止时间后发布，系统会发出严重警告。
        + 发布后，纪要的PDF版本自动归档到项目文件夹的 6可交付成果 > 会议纪要 目录中。
    * 知识沉淀与复用:
        + 所有收集到的客户方法文档、模板和说明，自动归档到项目文件夹的 客户方法 子目录中。
        + 平台会询问：“此客户的方法论是否适用于未来项目？” 如果是，这些资料将被标记为“可复用资产”，供其他项目参考。
    * 下游流程触发:
        + 一旦此会议完成，平台会自动在项目日历上创建一个提醒：“请在48小时内安排‘必需的项目管理可交付成果文档审查会议’”，因为文档指出“将必需的项目管理可交付成果会议安排在计划进度确定会之后”，而此会议是前提。


###模块二：项目规划中心 (Project Planning Hub)

这是平台的核心，将复杂的规划工作分解为可管理的步骤。

####功能2.1：工作计划构建器 (Work Plan Builder)

此功能是PMP ProGuide平台的核心，旨在将文档中复杂的“工作计划三步法”（步骤1：收集信息，步骤2：团队评审，步骤3：职能经理审核）转化为一个结构化、防错的、向导式的数字化流程。

- 整体界面: 一个分阶段的向导式工作流，用户必须按顺序完成每个步骤，才能进入下一步。界面左侧显示清晰的进度条（Step 1 -> Step 2 -> Step 3 -> Final Review）。
步骤1：收集信息 (Work Plan Step 1)

此步骤的目标是秘密地从领域专家那里收集信息，而不在他们面前创建或修改工作计划。

- 引导: 用户点击“开始步骤1”，平台提示：“此步骤的目标是收集信息，而非创建计划。请勿在会议中打开MS Project。”
- 会议安排:
    * 自动化: 点击“安排会议”，平台自动调用“Outlook模板”中的“工作计划步骤1会议”模板。
    * 预填充: 会议主题自动设置为 [项目名称]:工作计划步骤1会议。
    * 邀请人: 平台引导用户从项目人员库中选择“领域专家”（团队成员）进行邀请，并明确提示：“不要邀请职能经理”。
    * 议程: 会议邀请中自动包含完整的“工作计划步骤1会议议程”。
- 信息收集:
    * 界面: 会议后，用户进入一个表单式界面，用于录入收集到的信息。
    * 内容:
        + 任务 (Tasks): 文本框，用户输入从团队成员处收集到的所有任务。平台会根据“项目管理社区工作计划规则”进行实时检查（如：任务是否在大纲级别4，是否包含动词）。
        + 工作量 (小时): 数字输入框，记录每个任务所需的工时。平台强制规则：“不要收集任务持续时间(天数)或开始/结束日期”。
        + 前/后接续关系 (Dependencies): 下拉选择或拖拽式界面，用于定义任务之间的逻辑关系。
        + 项目团队成员分配 (Assignment): 从项目人员库中选择负责该任务的团队成员。
        + 外部依存关系 (External Dependencies): 用于记录项目外部的依赖项。
- 工作计划生成:
    * 自动化计算: 用户提交信息后，平台自动执行以下操作：
        + 打开工作计划模板。
        + 将所有信息填入大纲级别4。
        + 根据“半场制工作模式”自动计算持续时间 (天数): 平台内置规则，根据任务工时自动换算为持续时间。
        + 应用所有“项目管理社区工作计划规则”（如大纲级别、范围项目持续时间限制等）。
    * 保存: 自动按照 [YYMMDD][项目名称]工作计划.mpp 的格式保存到 1项目计划 文件夹。
步骤2：团队评审 (Work Plan Step 2)

此步骤的目标是与团队成员一起审查工作计划的准确性，确保信息正确无误。

- 引导: 平台提示：“此步骤的目标是与团队成员确认信息的正确性，而非修改计划。请勿在会议中直接修改MS Project文件。”
- 会议安排:
    * 自动化: 点击“安排步骤2会议”，平台调用“工作计划步骤2会议”模板。
    * 预填充: 主题设置为 [项目名称]:工作计划步骤2会议。
    * 邀请人: 邀请在步骤1中被分配了任务的“团队成员”。
    * 议程: 自动包含“工作计划步骤2的会议议程”。
- 评审与反馈:
    * 界面: 会议前，平台将工作计划的PDF版本发送给参会者审阅。
    * 平台内协作: 团队成员可以在平台的“评审视图”中，对工作计划的特定任务发表评论（如：“此任务的工时估计不足”、“前/后接续关系有误”）。
    * 会议目标: 会后，用户在平台上确认是否获得了团队成员对以下几点的认同：
        + 所有任务均已正确表述。
        + 任务持续时间（天数）设置正确。
        + 前/后接续关系正确。
        + 人员分配正确。
- 修订: 如果有反馈需要修改，用户在会后根据笔记在工作计划中进行修改，然后重新生成PDF。
步骤3：职能经理审核 (Work Plan Step 3)

此步骤的目标是获得职能经理对其团队成员工作量和进度的最终认可。

- 引导: 平台提示：“此步骤的目标是获得职能经理对其团队成员工作量和进度的承诺。”
- 会议安排:
    * 自动化: 点击“安排步骤3会议”，平台调用“工作计划步骤3会议”模板。
    * 预填充: 主题设置为 [项目名称]:工作计划步骤3会议。
    * 邀请人: 邀请被分配任务的“团队成员”及其“职能经理”。
    * 议程: 自动包含“工作计划步骤3的会议议程”。
- 审查与承诺:
    * 界面: 会议中，平台引导用户重点审查“项目进度表”（即甘特图部分）。
    * 关键交互: 如果职能经理提出工作量冲突，平台提供解决方案引导：
        + 与团队成员协商调整档期。
        + 将任务开始日期延后，并自动在MS Project中将任务的“约束类型”设置为“开始不早于”。
    * 获取认可: 会后，用户必须在平台上记录：
        + 职能经理和团队成员对工作计划中任务、进度表和人员分配的共同认可。
        + 他们对“角色矩阵”和“沟通计划”的接受。
最终审查与批准 (Final Review & Approval)

在完成前三步后，工作计划才能进入最终审查。

- 流程: 用户发起“计划文件审查会议”和“计划文件批准会议”。
- 平台集成: 平台自动将工作计划、角色矩阵、沟通计划打包，按照文档要求发送给项目经理和项目负责人进行审查。
- 结果: 一旦获得所有批准，平台自动将工作计划“设定基准 (Set Baseline)”，并生成最终的“已批准的计划进度表PDF”，为项目执行阶段提供基准。
通过以上深化设计，PMP ProGuide平台将“工作计划构建”这一核心且复杂的流程，变成了一个零容错、强引导的数字工作流，确保即使是项目管理小白，也能严格按照120VC的标准，产出高质量、可执行的工作计划。



####功能2.2：计划进度表发布 (Schedule Release)

此功能是将“工作计划构建器”产出的工作计划，通过一个标准化、强制性的“批准-接受”流程，转化为项目执行基准的核心模块。它严格遵循文档中“完成计划进度表的批准和接受”章节的每一步骤。

- 整体界面: 一个线性的、四阶段的向导式工作流（文件审查 -> 批准 -> 验收 -> 最终归档），用户必须按顺序完成每个阶段，才能进入下一阶段。平台会实时检查每个阶段的前置条件是否满足。
阶段1：文件审查 (File Review)

此阶段的目标是获得项目经理（上级）的内部质量保证和批准，以确保计划的完整性和合理性。

- 引导: 用户点击“发起文件审查”，平台提示：“此会议是获得项目经理对计划文件进行质量保证的关键步骤。”
- 自动化安排:
    * 创建会议: 平台自动调用“Outlook模板”中的“计划文件审查会议”模板。
    * 预填充: 会议主题自动设置为 [项目名称]:计划文件审查会议。
    * 邀请人: 自动邀请“项目经理”。
    * 议程: 自动附上“计划文件审查会议议程”。
- 文件准备与发送:
    * 自动生成文件包: 在会议召开前一天，平台自动准备以下文件，并打包发送给项目经理：
        + 工作计划 (MS Project 文件): YYMMDD[项目名称]工作计划.mpp
        + 项目工作分解结构 (WBS PDF): YYMMDD[项目名称]WBS.pdf (平台根据文档要求，自动将工作计划导出为大纲级别3的WBS PDF)。
        + 角色矩阵 (Word 文件): YYMMDD[项目名称]角色矩阵.doc
        + 沟通计划 (Word 文件): YYMMDD[项目名称]沟通计划.doc
    * 智能提醒: 平台会提醒用户：“所有文件必须在会议前一天的营业时间开始前发送，以确保项目经理有足够时间审阅。”
- 审查与批准:
    * 会议后操作: 会议结束后，用户在平台上记录结果。
    * 获取电子批准: 平台引导用户发起“电子批准请求”：
        + 自动生成一封邮件（使用“需要批准：[项目名称]计划文件和用于管理/控制和完成的WBS”模板）。
        + 收件人为“项目经理”，附件为WBS PDF和计划文件PDF。
        + 用户发送邮件，等待回复。
    * 状态更新: 一旦收到项目经理的批准回复，用户在平台上标记此阶段为“已完成”，并上传批准邮件的PDF存档。
阶段2：批准会议 (Approval Meeting)

此阶段的目标是获得项目负责人和高层利益相关者的正式批准和认可。

- 引导: 平台提示：“此会议是获得项目负责人和高层支持的关键。请使用WBS进行审查，而非详细的工作计划。”
- 自动化安排:
    * 创建会议: 平台自动调用“Outlook模板”中的“计划文件批准会议”模板。
    * 预填充: 会议主题自动设置为 [项目名称]:计划文件批准会议。
    * 邀请人: 引导用户邀请“项目负责人”和所有“高层管理者利益攸关方”。
    * 议程: 自动附上“计划文件批准会议议程”。
- 智能提示与文件准备:
    * 核心提示: 平台在安排会议时，会显著弹出提示：“请使用WBS而非工作计划进行审查。 工作计划过于详细，不适合向高层汇报。WBS提供了合适的详细程度。”
    * 文件发送: 在会议前一天，平台自动将已批准的WBS PDF、角色矩阵和沟通计划的PDF版本发送给所有参会者。
- 会议执行与记录:
    * 防错机制: 平台内置规则，如果用户在会议记录中未记录“项目负责人批准”和“高层管理者利益攸关方认可”，则无法标记此阶段为完成。
    * 变更管理: 如果会议中提出更改需求，平台引导用户：
        + 在会议记录中详细记录所有更改请求。
        + 不立即修改文件。
        + 会后根据记录修改工作计划，然后重新生成WBS PDF。
- 整合批准: 会议结束后，平台引导用户将收到的电子批准（来自项目经理）和会议中获得的批准/认可信息，整合到“A&A表格”中。
阶段3：验收会议 (Acceptance Meeting)

此阶段的目标是确保执行团队（团队成员及其职能经理）理解并接受他们的任务分配。

- 引导: 平台提示：“此会议是确保团队成员和职能经理对任务分配达成共识的关键步骤。”
- 自动化安排:
    * 创建会议: 平台自动调用“Outlook模板”中的“计划进度表验收会议”模板。
    * 预填充: 会议主题自动设置为 [项目名称]:计划进度表验收会议。
    * 邀请人: 自动邀请所有“接受了工作计划中分配任务的团队成员”及其“职能经理”。
    * 议程: 自动附上“计划进度表验收会议议程”。
- 文件发送与审查:
    * 文件发送: 在会议前一天，平台自动将更新后的、整合了所有批准的“已批准的计划进度表PDF”发送给所有参会者。
    * 审查重点: 平台提示用户，会议重点是“对当前和下一个时期的工作进行全面审查”，并“回答团队成员提出的问题”。
- 获取接受 (Acceptance):
    * 现场确认: 会议结束后，用户在平台上记录：“已获得所有参会团队成员和职能经理的口头接受”。
    * 追加接受: 对于未参会的人员，平台自动触发“追加接受”流程：
        + 自动生成一封邮件（使用“接受要求：[项目名称]计划进度表”模板）。
        + 收件人为未参会的团队成员及其职能经理。
        + 邮件要求对方回复邮件表示接受。
    * 5×5沟通法提醒: 如果在24小时内未收到回复，平台会弹出提醒：“请启动5×5沟通法（5次沟通尝试），确保在3天内获得所有接受。”
阶段4：最终归档与发布 (Final Archival & Publication)

此阶段是流程的终点，将所有信息整合成最终的、可执行的基准文档。

- 整合最终版:
    * 自动化: 一旦所有电子接受邮件收到，平台引导用户将所有批准/接受的邮件PDF，作为注释添加到A&A表格中。
    * 生成最终PDF: 平台提供“生成最终批准版”按钮，一键将“已批准的计划进度表PDF”、“带注释的A&A表格”和所有“电子接受邮件”合并成一个最终的PDF文档。
- 命名与归档:
    * 自动命名: 平台根据文档要求，自动将最终文件命名为 [YYMMDD][项目名称]已批准的计划进度表.pdf (例如: 120108 GISP已批准的计划进度表.pdf)。
    * 自动归档: 文件自动保存到项目文件夹的 1项目计划 > 项目章程&计划文档存档 目录中。
- 发布与清理:
    * 发布: 平台提供“发布给项目团队”按钮，一键将最终版PDF发送给所有项目相关人员。
    * 清理: 平台提醒用户：“请删除所有历史版本的计划文件，以避免混淆。”
通过以上深化设计，PMP ProGuide平台将“计划进度表发布”这一复杂的多步骤批准流程，变成了一个不可逾越、防错、且高度自动化的数字工作流，确保项目管理小白也能产出一个经过完整审批、具有法律效力的项目执行基准。



####功能2.3：必需可交付成果确定 (Required Deliverables)

此功能旨在将文档中“必需的项目管理可交付成果文档审查会议”和“文件批准会议”的流程，转化为一个强制性的、防错的决策向导，确保项目从一开始就明确交付范围，避免后期混淆。

- 整体界面: 一个分两步的决策向导（文件审查 -> 文件批准），用户必须按顺序完成。
步骤1：文件审查 (File Review)

此步骤的目标是与项目经理合作，审查并确定项目需要哪些标准可交付成果。

- 引导: 用户点击“开始文件审查”，平台提示：“此会议是与项目经理共同确定项目必需可交付成果的关键步骤。请在计划进度确定会之后安排。”
- 自动化安排:
    * 创建会议: 平台自动调用“Outlook模板”中的“必需的项目管理可交付成果文档审查会议”模板。
    * 预填充: 会议主题自动设置为 [项目名称]:必需的项目管理可交付成果文档审查。
    * 邀请人: 自动邀请“项目经理”。
    * 议程: 自动附上“必需的项目管理可交付成果会议的议程”。
- 交互式决策流程:
    * [ ] 确定不需要的标准交付成果
        + 界面: 一个可勾选的列表，列出本书中定义的所有标准可交付成果（如项目章程、工作计划、状态报告等）。
        + 交互: 用户与项目经理共同讨论，对于可以省略的标准可交付成果进行勾选。
        + 强制风险提示: 当用户勾选任何一个标准可交付成果时，平台立即弹出严重警告：“不使用标准可交付成果可能增加客户失败的风险。 您确定要省略 [可交付成果名称] 吗？” 用户必须点击“确认”才能继续。平台会记录此决策和警告。
    * [ ] 确定必需的客户交付成果
        + 界面: 一个输入框和文件上传区。
        + 交互: 用户在此处输入或上传客户方法论要求的、但本书未涵盖的必需的客户特定可交付成果。例如，客户可能要求一个“安全合规报告”。
        + 引导: 提示语：“请列出所有客户要求的、超出本书范围的必需交付物。”
- 生成草案: 完成以上两项后，平台自动生成一份“必需的项目管理可交付成果”文档草案。
步骤2：文件批准 (File Approval)

此步骤的目标是获得项目经理的正式批准，使决策具有约束力。

- 引导: 平台提示：“此会议的唯一目标是获得项目经理对‘必需的项目管理可交付成果’文档的口头批准。”
- 自动化安排:
    * 创建会议: 平台自动调用“Outlook模板”中的“必需的项目管理可交付成果文件批准会议”模板。
    * 预填充: 会议主题自动设置为 [项目名称]:必需的项目管理可交付成果文件批准。
    * 邀请人: 自动邀请“项目经理”。
    * 议程: 自动附上“必需的项目管理可交付成果文件批准会议议程”。
- 批准流程:
    * 发送文件: 会议前，平台自动将“必需的项目管理可交付成果”文档草案的PDF版本发送给项目经理审阅。
    * 获取口头批准: 会议中，项目经理对文档进行审核并口头批准。
    * 获取电子批准 (关键步骤):
        + 引导: 会议结束后，平台引导用户发起“电子批准请求”。
        + 自动化: 平台自动生成一封邮件（使用“需要批准：[项目名称]必需的PM可交付成果的文件”模板）。
        + 收件人: “项目经理”。
        + 附件: “必需的项目管理可交付成果”文档的PDF版本。
        + 正文: “请回复此邮件，以批准上述文件。”
- 最终归档与发布:
    * 创建最终版: 一旦收到项目经理的批准回复，用户在平台上点击“生成最终批准版”。
    * 自动化归档: 平台执行以下操作：
        + 将项目经理的批准邮件打印为PDF。
        + 将该PDF作为附件，插入到“必需的项目管理可交付成果”文档的末尾。
        + 按照文档要求的命名规则，自动将文件命名为 YYMMDD[项目名称]批准的必需的PM可交付成果文件.pdf (例如: 120108GISP批准的必需的PM可交付成果文件.pdf)。
        + 自动将最终版文件保存到项目文件夹的 6可交付成果 > 必需的PM可交付成果文档存档 目录中。
    * 发布: 平台提供“发布给项目团队”按钮，一键将最终版清单发送给所有项目相关人员，确保信息同步。
通过以上深化设计，PMP ProGuide平台将“必需可交付成果确定”这一潜在的模糊环节，变成了一个有据可查、有迹可循、且与后续所有交付物强关联的正式流程，从根本上杜绝了“这个文件到底要不要做”的争议，为项目的顺利执行奠定了坚实的基础。



###模块三：执行与监控面板 (Execution & Monitoring Dashboard)

这是项目运行时的“驾驶舱”，让小白也能轻松掌控项目状态。

####功能3.1：健康状态仪表盘 (Health Status Dashboard)

此功能是PMP ProGuide平台的“驾驶舱”，它将文档中复杂的健康状态评判标准和计算方法，转化为一个实时、自动、可视化的决策支持中心。

- 整体界面: 一个分层的、多维度的仪表盘，包含“项目总览”、“任务健康”、“障碍优先级”和“关键路径”四个核心视图。
视图1：项目总览 (Project Overview)

此视图提供项目的整体健康状况，严格遵循文档中“记住项目健康状况的步骤”和“项目健康状态——适用于：项目负责人、项目经理&项目组合经理”的设计目标。

- 核心指标:
    * 项目健康状态 (Project Health): 一个巨大的红、黄、绿三色指示灯。
    * 计算逻辑 (自动化):
        + 平台自动扫描工作计划中所有健康状态为“红色”或“黄色”的任务。
        + 对于每一个此类任务，平台检查其“时间裕量”。
        + 规则1: 如果存在一个健康状态为“红色”且 时间裕量 - 延迟天数 <= 10 的任务，则项目健康状态为 红色。
        + 规则2: 如果不存在满足规则1的红色任务，但存在一个健康状态为“黄色”且 时间裕量 - 延迟天数 <= 10 的任务，则项目健康状态为 黄色。
        + 规则3: 如果以上两种情况都不存在，则项目健康状态为 绿色。
    * 专家建议提示: 仪表盘旁会有一个小图标，点击后显示：“敢于将项目标记为黄色或红色，是获取资源、避免项目失败的正确做法。这应被奖励，而非惩罚。”
- 计划 vs. 实际 (Planned vs. Actual):
    * 一个对比图表，显示项目计划的完成百分比与实际完成百分比。
    * 智能分析: 如果项目为红色或黄色，平台会自动分析“计划&实际进度”，并高亮显示实际进度落后于计划进度最严重的项目，作为支持优先级的依据。
视图2：任务健康 (Task Health)

此视图展示所有任务的健康状况，为职能经理和项目参与者提供优先级排序的依据。

- 核心指标:
    * 任务列表: 一个可排序、可筛选的表格，列出所有任务。
    * 健康状态 (Task Health): 根据文档中“记住任务状态的健康标准”自动计算。
        + 绿色 (正常运行): 任务按计划进行。
        + 黄色 (略有延迟): 任务落后于计划。
        + 红色 (延期): 任务已延期。
    * 时间裕量 (Float): 平台根据任务的前后接续关系，自动从工作计划中提取或计算每个任务的“时间裕量”。
    * 智能排序与高亮:
        + 默认排序: 按“时间裕量”升序排列，确保“时间裕量最少”的任务排在最前面。
        + 高亮显示: 将“时间裕量 <= 10天”的所有任务（即关键路径任务）用粗体或边框闪烁等方式高亮显示。
        + 提示: “时间裕量少于或等于10天的任务，都被视为处于项目的关键路径上。”
视图3：障碍优先级 (Obstacle Priority)

此视图是解决障碍的核心工具，基于“延迟日期”对障碍进行自动排序。

- 核心指标:
    * 障碍列表: 一个按“延迟日期”升序排列的表格。
    * 延迟日期 (Delay Date): 平台根据文档中“如何计算延迟日期”的步骤自动计算。
        + 复制当前工作计划。
        + 找到因障碍而落后或延期的第一个任务。
        + 将该任务的持续时间增加其“时间裕量”天数。
        + 新的完成日期即为“延迟日期”。
    * 障碍健康状态 (Obstacle Health): 自动继承其影响的最不健康任务的健康状态。
    * 迫在眉睫的障碍 (Imminent Obstacle) 警报:
        + 规则: 如果 延迟日期 - 当前日期 <= 10个工作日，则该障碍被标记为“迫在眉睫的障碍”。
        + 视觉警示: 在障碍列表中，此类障碍会显示为红色闪烁的警示条。
        + 强制行动: 点击该障碍，平台会弹出“必须在2个工作日内完成障碍评估”的提醒，并提供一键启动“障碍评估”流程的按钮。
视图4：关键路径 (Critical Path)

此视图为项目管理者提供深入分析。

- 核心指标:
    * 甘特图视图: 一个简化的甘特图，清晰地标出关键路径。
    * 关键路径识别: 平台自动识别并高亮所有 时间裕量 = 0 的任务链。
    * 动态模拟: 用户可以模拟某个关键路径任务延迟一天，平台会立即更新整个项目的完成日期，直观展示其影响。
自动化与集成

- 数据源: 仪表盘的所有数据都实时来源于“工作计划”和“控制日志”。
- 自动更新: 每当用户在控制日志中更新任务的完成百分比或在工作计划中调整任务时，仪表盘会在几秒内自动刷新，重新计算所有健康状态、时间裕量和延迟日期。
- 与报告集成: “项目状态报告”模块中的健康状态、任务列表和障碍列表，直接从仪表盘中抓取数据，确保信息一致性。
通过以上深化设计，PMP ProGuide平台的健康状态仪表盘不再是一个简单的状态展示，而是一个集成了评判、计算、预警和决策支持的智能中心，让项目管理小白也能像专家一样，精准地掌控项目脉搏。



####功能3.2：每日/每周状态报告 (Daily/Weekly Status Report)

此功能是PMP ProGuide平台的“核心交付物”，它将文档中复杂的报告编制流程，转化为一个高度自动化、防错、且符合沟通原则的标准化流程。

子功能3.2.1：每日状态报告 (Daily Status Report - DSR)

此功能旨在将“每日状态报告”打造成一个项目管理者的“每日仪式”，既是个人反思工具，也是向上沟通的桥梁。

- 界面: 一个简洁的、三栏式表单，完全对应报告的三个部分。
- 内容与自动化:
    * [ ] 三个感恩 (Three Gratitudes)
        + 引导: 界面提供“最佳实践”提示：“请写与今天相关的、具体的、积极的事件。例如：‘感谢团队成员在下午3点的会议上提出的那个绝妙点子，它解决了我们一个棘手的问题。’”
        + 交互: 三个独立的文本框，用户输入后可一键保存为草稿。
    * [ ] 今天的成就 (Today's Achievements)
        + 智能填充: 平台提供“从日历/任务中导入”按钮。点击后，用户可以选择今天已完成的会议或任务。
        + 防错引导: 平台内置“成就”标准，会实时检查用户输入：
            - 如果用户输入“参加了项目审查会议”，系统会弹出提示：“‘参加会议’不是一项成就。请描述在会议中取得的成果，例如：‘在项目审查会议上，与团队成员就A任务的解决方案达成共识，已更新控制日志。’”
            - 提示语：“成就必须是推动了项目、任务或客户关系的已完成事项。”
        + 来源: 成就信息主要来源于用户当天在控制日志中更新的任务状态。
    * [ ] 明天的计划成就 (Tomorrow's Planned Achievements)
        + 智能推荐: 平台根据“健康状态仪表盘”的数据，自动推荐高优先级任务。例如：“根据仪表盘，任务‘[任务名称]’时间裕量仅为2天，建议将其列为明日计划成就。”
        + 来源: 信息主要来源于工作计划中第二天计划开始或继续的任务。
- 发布与归档:
    * 一键发布: 用户确认内容后，点击“发布”。
    * 自动化发送: 平台自动调用“Outlook模板”中的“每日状态报告(DSR)模板”，生成邮件。
        + 收件人: 仅发送给“项目经理”。
        + 主题: 自动按 [MM/DD/YY]的每日状态报告 格式生成。
        + 正文: 自动将三个部分的内容，按文档要求的格式（加粗、下划线）填充到邮件正文中。
    * 强制规则: 平台内置日程提醒：“每日状态报告必须在晚上20:00前提交”。如果用户尝试在20:00后发布，系统会发出警告。
    * 自动归档: 发布后，平台自动将该邮件的PDF版本保存到本地 Projects>DSR 文件夹，并按 YYMMDD每日状态报告.pdf 命名。
子功能3.2.2：每周项目状态报告 (Weekly Project Status Report)

此功能是项目管理的“每周快照”，为所有利益相关者提供项目全景。

- 界面: 一个结构化的向导式模板，引导用户按文档规定的顺序完成报告。
- 内容与自动化:
    * [ ] 基本信息 (Header Information)
        + 自动填充: 项目名称、项目负责人、项目管理者、开始日期等字段，自动从项目主数据中获取。
        + 报告周期: 自动计算本周的起止日期（周一至周五）。
        + 项目运行状况: 关键自动化！ 平台直接从“健康状态仪表盘”读取项目健康状态（红/黄/绿），并自动填入。用户无法手动修改，确保了数据的客观性和一致性。
        + 实际完成百分比: 自动从工作计划中提取第0行的“实际完成百分比”并填入。
    * [ ] 状态总结 (Status Summary)
        + 智能引导: 平台根据“项目运行状况”自动提供写作框架：
            - 如果项目为绿色：提示“简要总结本周成就，描述下周计划。”
            - 如果项目为黄色或红色：强制要求用户填写，并提供模板：“1. 障碍说明： 项目因[障碍描述]导致[任务]延期，可能影响项目结束日期。2. 解决路径： 已采取[步骤1]、[步骤2]等措施，预计在[日期]解决。3. 新成就： 本周完成了[成就1]、[成就2]...”
        + 防错: 平台会检查内容是否回答了“项目为何不是绿色？”、“有何新成就？”、“障碍如何解决？”这三个核心问题。
    * [ ] 近期成就 (Recent Achievements - Past 30 Days)
        + 智能推荐: 平台从过去四周发布的状态报告和工作计划中，提取已完成的、具有里程碑意义的任务，作为候选成就供用户选择。
        + 交互: 用户从列表中选择最多5项，并确认完成日期。
    * [ ] 计划成就 (Planned Achievements - Next 30 Days)
        + 智能推荐: 平台从工作计划中，提取未来30天内计划完成的关键任务和里程碑。
        + 优先级引导: 如果项目为黄色或红色，平台会突出显示那些能将项目状态“变回绿色”的任务，并建议用户将其列为首要计划成就。
    * [ ] 项目障碍 (Project Obstacles)
        + 完全自动化: 关键功能！ 用户无需手动输入。
        + 数据源: 平台直接从“控制日志”中筛选出类型为“项目障碍”的条目。
        + 排序: 自动按“延迟日期”从早到晚排序。
        + 可视化: 自动生成一个表格图片，包含“障碍名称”、“延迟日期”、“接受任务者”、“健康状态”等关键列，并直接嵌入报告。这完全符合文档中“复制为图片”的要求。
    * [ ] 当前和下一个时期计划任务 (Current & Next Period Tasks)
        + 完全自动化: 关键功能！
        + 数据源: 平台自动从工作计划中导出。
        + 逻辑: 执行文档中的步骤：
            - 打开工作计划，选择“大纲级别4”。
            - 选择“工作计划打印视图”，输入“再下一个星期五”的日期。
            - 筛选出“完成率未达到100%”且计划在“当前时间段或下一个星期五”或“下一个星期五和再下个星期五之间”开始的所有任务。
        + 可视化: 自动生成一个任务列表的表格图片，并直接嵌入报告。
- 发布与归档:
    * 一键发布: 用户确认内容后，点击“发布”。
    * 自动化打包与发送:
        + 生成文件: 平台自动生成：
            - 报告Word文档（按 [YYMMDD][项目名称]项目状态报告.doc 命名）。
            - 报告PDF版本。
            - 工作计划的PDF版本（按 [YYMMDD][项目名称]工作计划.pdf 命名）。
            - 控制日志的Excel版本。
            - “当前和下一个时期计划任务”的Excel版本。
        + 创建邮件: 平台自动调用“项目状态报告”模板，生成Outlook邮件。
            - 收件人: 自动从“角色矩阵”中获取所有“项目团队-角色列表”中的成员。
            - 主题: 按 [项目名称]项目状态报告[MM/DD/YY] 格式生成。
            - 正文: 自动将报告的“状态总结”部分粘贴到邮件正文中，并按格式添加“附件”标题。
            - 附件: 自动将上述生成的所有文件作为附件添加。
        + 发送: 用户确认后，一键发送。
    * 自动归档: 发送后，平台自动将邮件的PDF版本保存到项目文件夹的 4个项目状态报告>状态报告PDF存档 目录中。
通过以上深化设计，PMP ProGuide平台将“每日/每周状态报告”这一耗时且易出错的流程，变成了一个由系统驱动、数据支撑、格式规范的高效自动化流程，确保项目管理小白也能持续、高质量地输出专业报告，有效管理所有利益相关者的期望。



####功能3.3：障碍管理 (Obstacle Management)

此功能是PMP ProGuide平台的“危机处理中心”，它将文档中复杂的障碍管理流程（从发现到评估、批准、解决）转化为一个强制性、防错、且与“健康状态仪表盘”深度集成的闭环工作流。



整体界面: 一个独立的“障碍看板 (Obstacle Kanban)”，包含“待处理”、“迫在眉睫”、“已评估”、“已批准”、“已解决”五个状态列。所有障碍均按“延迟日期”升序排列。

阶段1：创建障碍 (Create Obstacle)

此步骤是障碍管理的起点，必须在“控制日志”中进行。



引导: 用户在“控制日志”中创建新条目，当“任务/障碍”类型选择“I”（障碍）时，平台自动触发“障碍管理”流程。

- 必填信息:
障碍名称: 文本框。平台内置强引导：“请描述障碍本身，而非其影响。 例如，‘DeliveryInc在运送过程中丢失了服务器’，而非‘服务器的安装被延迟’。”

延迟日期: 日期选择器。关键自动化！ 平台提供“计算延迟日期”按钮。点击后，自动执行文档中的计算步骤：

创建工作计划副本。

找到因该障碍而落后或延期的第一个任务。

获取该任务的“时间裕量”。

将该任务的持续时间增加其“时间裕量”天数。

将新计算出的完成日期自动填入“延迟日期”字段。

接受任务者: 下拉选择，必须从“角色矩阵”中选择一人，确保责任制（TBD状态最多持续一个报告周期）。

启动日期 & 完成日期: 日期选择器，用于记录障碍被发现的日期和承诺解决的日期。

保存与触发: 保存后，该障碍条目自动出现在“障碍看板”的“待处理”列，并开始被“健康状态仪表盘”监控。

阶段2：评估 (Assessment)

此阶段的目标是为“迫在眉睫的障碍”创建“障碍评估”文档，以获取高层批准。



- 自动升级:
规则: 当某个障碍的 延迟日期 - 当前日期 <= 10个工作日 时，平台自动将其从“待处理”列移动到“迫在眉睫”列，并触发评估流程。

强制行动: 看板上，该障碍条目会显示一个红色闪烁的警示图标和倒计时：“必须在[倒计时]天内完成障碍评估”。

- 启动评估流程:
引导: 用户点击“迫在眉睫”列中的障碍，平台弹出提示：“此障碍已升级为‘迫在眉睫’，请立即启动障碍评估流程。”

一键创建文档: 点击“创建障碍评估”，平台自动执行以下操作：

调用“项目模板”中的“障碍评估”模板。

自动填充元数据: 项目名称、障碍编号、障碍名称、延迟日期等信息从控制日志中自动同步。

打开文档，准备编辑。

- 填写障碍评估文档:
强制结构化输入: 平台引导用户按以下顺序填写，确保逻辑清晰：

障碍说明: 再次强调：“描述障碍本身及其原因”，并提供反例（如“服务器安装延迟”是错误的）和正例（如“运输途中丢失服务器”是正确的）。

影响分析: 分析该障碍对成本（金钱、人力）和进度（延迟天数）的影响。

建议的解决方案: 用户在此处定义一个最佳解决方案（简单方案或变更范围方案），并说明选择理由。平台会警告：“请只提出一个最佳方案，以展现您的决策力和信心。”

约束与假设: 记录执行该方案的已知约束和必要假设。

智能提醒: 文档中会嵌入“要点精炼”：“项目管理者只有10天时间来完成项目变更评估。”

阶段3：审批 (Approval)

此阶段的目标是获得项目经理和项目负责人的正式批准。



步骤3.1：文件审查 (File Review)

自动化安排: 用户点击“安排审查会议”，平台自动调用“障碍评估文件审查会议”模板。

预填充: 会议主题为 [项目名称]:障碍评估文件审查会议，邀请人仅为“项目经理”。

发送文件: 会议前，平台自动将“障碍评估”文档的MS Word版本发送给项目经理。

获取批准: 会议结束后，用户在平台上记录项目经理的口头批准，并发起“电子批准请求”，将PDF版本发送给项目经理确认。

步骤3.2：批准会议 (Approval Meeting)

自动化安排: 获得项目经理批准后，平台自动解锁“安排批准会议”按钮。点击后，调用“障碍评估文件批准会议”模板。

预填充: 会议主题为 [项目名称]:障碍评估文件的批准会议，邀请人仅为“项目负责人”。

发送文件: 会议前，平台自动将“障碍评估”PDF发送给项目负责人。

会议引导: 平台提供议程提示：“向项目负责人简要介绍，而非逐行检查。会议只有3种可能结果：1. 批准方案；2. 选择替代方案；3. 提供新方案。”

记录结果: 会议结束后，用户必须在平台上记录最终结果（批准的方案、替代方案或新方案）和任何关于“项目变更评估”的时间协议。

状态更新: 一旦记录结果，该障碍从“已评估”列移动到“已批准”列。

阶段4：解决 (Resolution)

此阶段是障碍管理的闭环，将批准的方案转化为可执行的任务。



- 自动创建解决任务:
触发: 当障碍状态变为“已批准”时，平台自动弹出提示：“是否要为批准的解决方案创建解决任务？”

一键创建: 用户点击“是”，平台自动执行：

在“控制日志”中创建一个新任务。

任务名称: 自动生成，如“解决[障碍编号]号障碍：[解决方案摘要]”。

关联: 该任务与原始障碍条目建立强关联，形成父子关系。

分配: 自动分配给“接受任务者”或由用户指定的负责人。

持续时间: 根据方案中评估的解决日期自动计算。

手动创建: 用户也可选择手动创建更复杂的任务链。

- 跟踪与关闭:
集成跟踪: “解决障碍”的任务会出现在“健康状态仪表盘”和“每日状态报告”中，其进度直接影响原始障碍的健康状态。

自动关闭: 当“解决障碍”任务的完成百分比达到100%时，平台会提示用户：“障碍已解决，是否关闭？”

最终归档: 用户确认后，平台将该障碍移动到“已解决”列，并将其从所有活跃报告中移除。同时，将“障碍评估”文档、批准邮件和解决任务的记录打包归档。

通过以上深化设计，PMP ProGuide平台的障碍管理功能，将一个被动的、反应式的“问题记录”，变成了一个主动的、前瞻式的“风险管理”和“决策支持”系统，让项目管理小白也能专业、高效地应对项目中的任何挑战。

####功能3.4：会议中心 (Meeting Hub)

此功能是PMP ProGuide平台的“中枢神经系统”，它将文档中所有零散的、手动的会议安排流程，整合为一个高度自动化、标准化、且与项目工作流深度绑定的集中式管理模块。

- 整体界面: 一个集成的日历视图（类似Outlook日历）和一个“会议库”面板。用户可以在此创建、查看、管理所有项目相关的会议。
核心功能1：模板化创建 (Template-Driven Creation)

此功能是会议中心的基石，确保所有会议都遵循120VC的标准。

- 会议类型库:
    * 界面: 一个下拉菜单或图标网格，列出所有预设的会议类型，例如：
        + 项目启动前与项目负责人的会面
        + 客户方法发现会议
        + 工作计划步骤1：文件审查
        + 工作计划步骤2会议
        + 工作计划步骤3会议
        + 计划进度文件审查会议
        + 计划进度表批准会议
        + 项目启动会议
        + 每周项目团队会议
        + 每周项目审查会议
        + 障碍评估文件审查会议
        + 障碍评估批准会议
        + 项目变更计划会议
        + 项目变更评估文件审核会议
        + 项目变更评估批准会议
        + 变更审核会议
        + 沟通审查会议
        + 预算文件审查会议
        + 基准预算文件批准会议 等。
- 自动化填充:
    * 选择类型: 用户选择一个会议类型后，平台自动应用该会议的预设配置。
    * 自动填充内容:
        + 时长 (Duration): 根据文档要求自动设置（如“客户方法发现会议”为1小时，“工作计划第一步：文件审查”为2小时）。
        + 议程 (Agenda): 自动加载该会议类型的完整议程表格，包括“议程项目”、“所有者”和“持续时间”。
        + 邀请人列表 (Invitees): 根据文档中的“所有者”和参会要求，自动建议邀请人。
            - 例如，创建“工作计划步骤2会议”时，自动建议邀请“领域专家（团队成员）”，并智能提示：“注意：不要邀请职能经理”。
            - 创建“计划进度表批准会议”时，自动建议邀请“项目负责人”和“高层管理者利益攸关方”。
        + 主题 (Subject): 自动生成符合文档命名规范的主题，如 [项目名称]:[会议类型]。用户可在此基础上微调。
        + 位置 (Location): 自动填充项目预设的Web会议信息或会议室。
核心功能2：自动化安排与同步 (Automated Scheduling & Sync)

此功能确保会议安排高效、无遗漏。

- 智能日程安排:
    * 依赖关系: 平台内置会议之间的逻辑依赖。例如，当用户完成“工作计划构建器”后，平台会自动提示：“下一步需要安排‘计划进度文件审查会议’”。
    * 时间建议: 平台会根据“要点精炼”中的时间要求，提供智能建议。
        + 例如，安排“计划进度表批准会议”时，系统会提示：“根据文档要求，此会议应在‘计划进度文件审查会议’的第二天营业时间内安排。”
        + 安排“每日状态报告”时，系统会自动在用户日历上创建一个每日20:00的提醒。
- 无缝同步:
    * 自动创建日历事件: 用户确认会议详情后，点击“创建”，平台立即在用户的Outlook/Google Calendar中创建一个日历事件。
    * 双向同步: 会议的任何变更（时间、议程）都会自动同步到用户的日历，反之亦然。
    * 强制提醒: 系统会在会议开始前15分钟发送强提醒，并附上会议链接和议程。
核心功能3：议程与记录一体化 (Integrated Agenda & Minutes)

此功能将会议的准备、执行和收尾无缝连接。

- 会前:
    * 议程预览: 会议详情页会清晰地展示完整的议程表格。
    * 文件准备提醒: 在会议召开前一天的营业时间开始之前，平台会自动触发一个任务：
        + 生成会议所需的所有文档（如WBS、工作计划、障碍评估等）。
        + 将这些文档作为附件，通过电子邮件发送给所有参会者。
        + 在会议详情页上标记“文件已发送”。
- 会中:
    * 实时协作: 会议详情页提供一个“实时笔记”区域。用户（项目管理者）可以在会议进行时，直接在此区域记录关键的“达成的一致意见”、“障碍”和“任务”。
    * 议程跟踪: 用户可以实时标记议程项目的完成状态。
- 会后:
    * 一键生成纪要: 会议结束后，用户点击“生成会议纪要”，平台自动执行以下操作：
        + 调用“会议记录”模板。
        + 自动填充元数据: 项目名称、日期、时间、位置、会议召集人、会议主题等信息自动同步。
        + 智能导入内容: 将“实时笔记”区域中的“达成的一致意见”、“障碍”和“任务”分类导入到纪要模板的对应部分。
    * 发布与归档:
        + 一键发布: 用户确认内容后，点击“发布”，平台自动：
            - 生成一封包含纪要PDF附件的Outlook邮件。
            - 收件人为所有参会者、未到会者和项目经理。
            - 主题按 [项目名称]会议纪要-[会议主题] 格式生成。
            - 发送邮件。
        + 自动归档: 发布后，平台自动将纪要的Word和PDF版本，按照文档要求的命名规则（YYMMDD[项目名称]会议纪要-[会议主题].doc/pdf）保存到项目文件夹的 5会议 > 会议纪要PDF存档 目录中。
    * 强制规则: 平台内置倒计时：“会议纪要必须在30分钟内发布，最晚不得迟于第二天下班时间。” 如果用户尝试在截止时间后发布，系统会发出严重警告。
通过以上深化设计，PMP ProGuide平台的会议中心，将文档中关于会议的“议程设置”、“安排”、“记录”和“发布”的所有要求，封装成一个流畅、自动化、且防错的闭环流程，让项目管理小白也能像专家一样，高效、专业地组织每一次会议，确保信息的准确传递和行动的快速落地。



###模块四：变更管理流程 (Change Management Workflow)

将复杂的变更评估流程标准化。

####功能4.1：变更请求 (Change Request)

此功能是PMP ProGuide平台的“变更引擎”，它将文档中复杂的“项目变更评估(PCA)”流程，转化为一个强制性的、向导式的、且与障碍管理流程紧密关联的闭环工作流。

- 整体界面: 一个分阶段的向导式工作流，用户必须按顺序完成每个步骤。流程分为四个核心阶段：请求与规划 -> 评估与文件编制 -> 审批 -> 验收与发布。
阶段1：请求与规划 (Request & Planning)

此阶段是变更流程的起点，旨在收集信息并启动规划。

- 引导: 用户点击“发起变更请求”，平台提示：“请描述变更需求。变更可能源于客户请求或迫在眉睫的障碍。”
- 提交表单:
    * 变更类型: 单选按钮，用户选择“客户请求”或“迫在眉睫的障碍”。
        + 如果选择“迫在眉睫的障碍”，平台会自动关联一个已存在的、状态为“迫在眉睫”的障碍。
    * 变更描述: 文本框，用户输入变更的简要说明。
    * 发起人: 下拉选择，选择提出变更需求的人员（项目负责人、客户代表等）。
    * 预期完成日期: 日期选择器。关键自动化！ 平台内置规则：“项目管理者只有10天时间来完成项目变更评估。” 因此，系统会自动将当前日期+10天作为默认的“预期完成日期”，用户可在此基础上调整。
- 自动创建任务:
    * 触发: 提交后，平台立即在“控制日志”中创建一个新任务。
    * 任务名称: 自动生成为“完成项目变更评估”。
    * 开始/结束日期: 开始日期为当前日期，结束日期为表单中的“预期完成日期”。
- 启动“变更计划会议”流程:
    * 自动化: 平台弹出提示：“下一步需要安排‘变更计划会议’以收集详细信息。”
    * 一键创建: 用户点击“安排变更计划会议”，平台自动：
        + 调用“Outlook模板”中的“变更计划会议”模板。
        + 预填充会议主题 [项目名称]:变更计划会议。
        + 建议邀请“领域专家（团队成员）”。
        + 附上“变更计划会议的议程”。
阶段2：评估与文件编制 (Assessment & Documentation)

此阶段是变更流程的核心，目标是完成所有必需的变更文档。

- 引导: 会议结束后，平台引导用户进入“项目变更评估”文档的创建流程。
- 创建“项目变更评估”文档:
    * 自动化: 点击“创建项目变更评估”，平台自动执行以下操作：
        + 调用“项目模板”中的“项目变更评估”模板。
        + 自动填充元数据:
            - 项目名称、变更ID (CID) (格式为 YYMMDD)、日期、发起人、变更类型。
            - 如果源于障碍，则自动填入“障碍编号#”。
    * 填写文档:
        + 变更的描述和影响: 平台提供向导，引导用户：
            - 比较“变更后的工作计划”与“已制定的工作计划”的项目完成日期。
            - 比较“变更后的基准预算”与“已制定的基准预算”的项目总成本。
            - 根据比较结果，简要说明对进度和成本的影响。
        + 修订后的概述和范围: 平台强调：“项目变更评估旨在取代项目章程。” 引导用户复制并更新项目章程的内容，并强制要求使用“文本突出显示”工具来标记所有更改的部分。
- 自动创建变更文件副本:
    * 变更工作计划:
        + 平台提示：“请创建变更工作计划的副本。”
        + 一键操作：用户点击“创建副本”，平台自动复制当前工作计划，并按 YYMMDD[项目名称]工作计划 CIDYYMMDD.mpp 的命名规则保存到 项目变更评估存档>CIDYYMMDD 文件夹。
        + 提醒: “变更后的工作计划需在项目变更评估批准后，保存到主项目计划文件夹。”
    * 变更基准预算:
        + 类似地，平台引导用户创建“预算报告”的副本。
        + 一键操作：点击“创建副本”，按 YYMMDD[项目名称]预算报告CIDYYMMDD.xls 命名并保存。
    * 角色矩阵和沟通计划附录 (如适用):
        + 如果变更涉及人员或沟通方式的改变，平台会自动提示用户创建“角色矩阵附录”和“沟通计划附录”。
        + 提供一键创建副本和更新的向导。
阶段3：审批 (Approval)

此阶段是变更流程的“质量门”，通过一系列会议获得各级批准。

- 步骤3.1：文件审核 (File Review)
    * 自动化安排: 用户点击“安排文件审核”，平台自动调用“项目变更评估文件审核会议”模板。
    * 预填充: 会议主题为 [项目名称]:项目变更评估文件审核，邀请人仅为“项目经理”。
    * 发送文件: 会议前一天，平台自动将“项目变更评估”、“变更工作计划”、“变更基准预算”等所有文档的PDF版本发送给项目经理审阅。
    * 获取批准: 会议结束后，用户在平台上记录项目经理的口头批准，并发起“电子批准请求”，获取批准邮件。
- 步骤3.2：批准会议 (Approval Meeting)
    * 自动化安排: 获得项目经理批准后，平台自动解锁“安排批准会议”按钮。点击后，调用“项目变更评估批准会议”模板。
    * 预填充: 会议主题为 [项目名称]:项目变更评估批准会议，邀请人仅为“项目负责人”和“高层利益攸关方”。
    * 发送文件: 会议前一天，平台自动将所有变更文档的PDF发送给项目负责人和高层。
    * 会议引导: 平台提供议程提示：“简要介绍文件内容，不要逐行检查。会议只有3种可能结果：1. 批准方案；2. 选择替代方案；3. 提供新方案。”
    * 记录结果: 会议结束后，用户必须在平台上记录最终结果和任何关于“变更审核会议”的时间协议。
- 步骤3.3：变更审核会议 (Change Review Meeting)
    * 自动化安排: 平台提示：“下一步需要安排‘变更审核会议’以获得团队接受。”
    * 一键创建: 用户点击“安排变更审核会议”，平台自动调用“变更审核会议”模板。
    * 预填充: 会议主题为 [项目名称]:变更审核会议，邀请人包括所有受影响的“团队成员”和“职能经理”。
    * 发送文件: 会议前一天，平台自动将所有变更文档的PDF发送给参会者。
    * 会议目标: 引导用户审查变更后的工作计划、角色矩阵和沟通计划附录，并获得团队的接受。
阶段4：验收与发布 (Acceptance & Publication)

此阶段是变更流程的终点，将所有信息整合并发布。

- 整合最终版:
    * 自动化: 一旦所有电子接受邮件收到，平台引导用户将所有批准/接受的邮件PDF，作为注释添加到A&A表格中。
    * 生成最终PDF: 平台提供“生成最终批准版”按钮，一键将“项目变更评估”、“变更工作计划”、“变更基准预算”、“角色矩阵附录”、“沟通计划附录”以及所有批准/接受文件合并成一个最终的PDF文档。
- 命名与归档:
    * 自动命名: 平台根据文档要求，自动将最终文件命名为 [YYMMDD][项目名称]批准的项目变更评估 CIDYYMMDD.pdf。
    * 自动归档: 文件自动保存到项目文件夹的 1项目计划 > 项目变更评估存档 > CIDYYMMDD 目录中。
- 发布与更新:
    * 发布: 平台提供“发布给项目团队”按钮，一键将最终版PDF发送给所有项目相关人员。
    * 更新基准: 平台提示：“请将变更后的工作计划和变更后的基准预算设定为新的项目基准。” 并提供一键操作的链接。
通过以上深化设计，PMP ProGuide平台的变更请求功能，将一个高风险、高复杂度的管理流程，变成了一个清晰、可控、且有时间约束的标准化工作流，让项目管理小白也能在10天内专业、高效地完成项目变更评估，确保项目始终在正确的轨道上运行。



####功能4.2：10天倒计时提醒

此功能是PMP ProGuide平台的“时间警报器”，它将文档中反复强调的“10天完成时限”转化为一个强制性的、高优先级的视觉和任务提醒，确保项目管理小白不会错过任何关键的截止日期。

- 核心原则: 该功能不仅适用于“项目变更评估(PCA)”，也同样适用于“障碍评估”，因为两者都遵循“10天内完成”的核心规则。
触发机制 (Trigger Mechanism)

倒计时提醒的启动是自动的，基于平台对关键事件的监测。

- 为“项目变更评估(PCA)”触发:
    * 触发条件: 当用户在“变更请求”模块中创建一个新的变更请求时，系统立即启动倒计时。
    * 计算逻辑: 倒计时的终点是 当前日期 + 10个工作日。平台会自动排除周末和法定节假日。
    * 智能调整: 如果用户在“变更请求”表单中手动设置的“预期完成日期”早于系统计算的10天期限，倒计时将遵循用户设置的更早日期。
- 为“障碍评估”触发:
    * 触发条件: 当“障碍看板”中的某个障碍，其“延迟日期”与“当前日期”的差值 ≤ 10个工作日 时，该障碍自动升级为“迫在眉睫的障碍”，并立即触发倒计时。
    * 计算逻辑: 倒计时的终点是 障碍升级为“迫在眉睫”状态的第二天营业时间结束前。这完全符合文档中“必须在此事件发生后的第二天营业结束之前，建立障碍评估机制”的严格要求。
提醒界面与功能 (Alert Interface & Functionality)

倒计时提醒以多种方式呈现，确保用户无法忽视。

- 任务面板高亮显示:
    * 位置: 在用户的个人“任务面板”或“待办事项”列表顶部，创建一个不可忽略的、高优先级的提醒卡片。
    * 视觉设计:
        + 颜色: 使用醒目的红色或橙色背景。
        + 图标: 显示一个闪烁的时钟或警报图标。
        + 文字: 清晰显示：“迫在眉睫！ 请在[倒计时天数]天内完成‘[文档名称]’”。
        + 动态更新: 倒计时天数每24小时自动更新一次。
    * 交互: 点击卡片，直接跳转到对应的“项目变更评估”或“障碍评估”工作流。
- 日历事件与提醒:
    * 自动创建: 平台自动在用户的日历上创建一个名为“完成[文档名称] - 最后期限”的事件。
    * 时间: 事件设置在倒计时的最后一天。
    * 提醒: 设置多重提醒（例如，提前3天、提前1天、当天上午9:00），确保用户收到通知。
- 每日状态报告 (DSR) 集成:
    * 自动提示: 在用户撰写每日状态报告时，如果存在未完成的“10天倒计时”任务，平台会在“明天的计划成就”部分上方，自动添加一条提示：“注意： 您有1个‘项目变更评估’需要在[天数]天内完成，请将其列为明日首要任务。”
    * 强制关联: 用户可以一键将“完成[文档名称]”作为“明天的计划成就”添加到报告中。
- 仪表盘 (Dashboard) 集成:
    * 项目总览: 在“健康状态仪表盘”的“项目总览”视图中，如果项目因一个未完成的“10天倒计时”任务而处于风险中，平台会显示一个显著的警告条：“存在10天内需完成的关键评估”。
    * 障碍看板: 在“障碍看板”中，“迫在眉睫的障碍”列的标题旁会显示一个总的倒计时，提示用户距离所有迫在眉睫障碍的评估截止日还有多少天。
超时处理与升级 (Escalation on Timeout)

如果用户未能在期限内完成，平台将启动升级流程。

- 超时提醒: 在截止日期当天，系统会发送一封高优先级的邮件和应用内通知：“严重警告： ‘[文档名称]’的10天期限已过！项目已处于高风险状态。”
- 自动上报: 平台会根据“角色矩阵”，自动将此超时事件通知给“项目经理”和“项目负责人”。
- 记录与审计: 超时事件将被记录在“控制日志”和“项目审计日志”中，作为项目风险管理的一部分。
通过以上深化设计，PMP ProGuide平台的“10天倒计时提醒”功能，不再是一个简单的计时器，而是一个集成了任务管理、日历提醒、报告集成和自动上报的综合性风险控制系统，它用最直接的方式，将120VC标准中对时间的严格要求，刻入了项目管理的每一个环节。



###模块五：知识库与帮助中心 (Knowledge Base & Help Center)

这是平台的“大脑”，确保用户随时能获取指导。

####功能5.1：上下文帮助 (Contextual Help)

此功能是PMP ProGuide平台的“内置教练”，它将文档中分散的“评判标准”、“设计目标”、“要点精炼”和“实践指南”等知识，精准地推送到用户最需要的时刻，实现“在正确的时间，给正确的用户，推送正确的知识”。

- 核心实现: 一个动态的、智能的知识弹窗系统，与平台的每一个功能模块和操作步骤紧密关联。
- 实现方式与交互:
- “？”按钮集成:
    * 在平台的每一个功能模块、每一个表单、每一个向导步骤的右上角，都嵌入一个醒目的“?”按钮。
    * 当用户将鼠标悬停在“?”按钮上时，会显示一个简短的提示，如“查看工作计划步骤1的指南”。
- 精准内容推送:
    * 点击“?”按钮后，弹出一个侧边栏或模态窗口，其内容是动态生成的，完全取决于用户当前所处的界面和操作。
    * 推送逻辑示例:
        + 在“工作计划构建器” -> “步骤1：收集信息”界面点击“?”
            - 推送内容:
                * 标题: 工作计划步骤1: 收集信息
                * 核心原则: “项目管理者不得在客户在场的情况下，在工作计划中输入或修改任务。”
                * 评判标准: “此步骤的目标是收集信息，而非创建计划。请勿在会议中打开MS Project。”
                * 要点精炼: “如果供应商也需要承担项目的任务，则在工作计划步骤1的会议中，邀请供应商团队成员出席非常重要。”
                * 行动指南: “会议结束后，您必须确定：1. 所有任务；2. 工作量(小时)；3. 前/后接续关系；4. 团队成员分配；5. 外部依存关系。”
        + 在“健康状态仪表盘” -> “项目总览”界面点击“?”
            - 推送内容:
                * 标题: 项目健康状态 - 评判标准
                * 设计目标: “项目经理每周对项目健康状况进行评级，以确定该项目是自给自足或需要增补资源才可正常推进。”
                * 评判标准:
                    + “如果项目存在富裕天数≤10的黄色任务，且不存在任务富裕天数≤10的红色任务，则项目运行状况为黄色。”
                    + “如果项目有一个富裕天数≤10的红色任务，则项目运行状况为红色，不管有多少个富裕天数≤10的黄色任务。”
                    + “如果项目不存在时间裕量少于10天的红色或黄色任务，则其运行状况为绿色。”
                * 专家建议: “敢于将项目标记为黄色或红色，是获取资源、避免项目失败的正确做法。这应被奖励，而非惩罚！”
        + 在“变更请求”表单的“预期完成日期”字段旁点击“?”
            - 推送内容:
                * 标题: 10天倒计时提醒
                * 核心规则: “项目管理者只有10天时间来完成项目变更评估。”
                * 要点精炼: “必须在此事件发生后的第二天营业结束之前，建立障碍评估机制。”
                * 行动指南: “从今天起计算，您必须在10个工作日内完成‘项目变更评估’文档的编制、审查和批准。”
- 高级功能:
- 知识图谱关联:
    * 帮助内容不仅仅是孤立的片段，而是相互关联的。
    * 例如，在阅读“项目健康状态”的评判标准时，内容中会嵌入可点击的链接，如“[任务的健康状态]”、“[如何计算延迟日期]”、“[时间裕量]”。点击这些链接，可以跳转到相关主题的详细帮助页面。
- 场景化学习路径:
    * 对于复杂的流程（如“工作计划三步法”），平台提供“学习此流程”按钮。
    * 点击后，会启动一个交互式向导，引导用户按顺序阅读“工作计划步骤1”、“工作计划步骤2”、“工作计划步骤3”的所有相关帮助内容，并附带流程图。
- “要点精炼”高亮显示:
    * 所有文档中的“要点精炼”部分，在帮助弹窗中都会用特殊的背景色（如黄色高亮）或图标（如灯泡图标）进行突出显示，确保用户不会错过关键的实践技巧。
- 版本化与更新:
    * 所有帮助内容都与《整本书.docx》的版本关联。当平台或方法论更新时，帮助中心的内容也会同步更新，并通知用户。
通过以上深化设计，PMP ProGuide平台的上下文帮助功能，从一个被动的“文档查询器”，转变为主动的“智能教练”，它能在用户迷茫或犹豫时，即时提供最权威、最精准的指导，确保项目管理小白在每一个决策点都能做出符合120VC标准的正确选择。



####功能5.2：学习中心 (Learning Center)

此功能是PMP ProGuide平台的“知识引擎”，它将文档中分散的“指南”、“议程”、“评判标准”和“要点精炼”等内容，转化为一系列结构化的、可交互的、与具体操作强关联的微学习模块。

- 核心理念: “Just-In-Time Learning” (即时学习)。学习内容不是孤立的，而是作为用户完成任务的“燃料”和“向导”。
- 内容组织与形式:
学习中心的内容将文档中的关键章节，转化为以下几种形式：

- 交互式向导 (Interactive Guides):
    * 形式: 模拟真实平台操作的分步教程。
    * 示例:
        + 《如何完成客户方法发现会议》: 用户点击后，会进入一个模拟的会议流程。系统会提示用户：“现在，你需要确定客户方法要求的所有标准可交付成果。” 用户需要在列表中进行勾选。完成后，系统继续引导：“接下来，确定每个可交付成果的目的/价值。” 用户输入后，系统自动进入下一步，直至完成整个流程。最后，系统会生成一份“学习完成证书”。
        + 《如何发布每周项目状态报告》: 一个完整的模拟流程，引导用户从填写基本信息、获取健康状态、到导出“当前和下一个时期计划任务”并生成PDF。
- 短视频 (Short Videos):
    * 形式: 3-5分钟的动画或真人讲解视频，解释核心概念。
    * 示例:
        + 《什么是延迟日期？》: 用动画演示一个任务、其时间裕量和障碍之间的关系，直观地展示“延迟日期”是如何计算的，以及为什么它对优先级排序至关重要。
        + 《5×5沟通法实战演练》: 一个情景剧，展示项目管理者如何通过5封邮件和5条语音信息，成功地让一个不响应的团队成员采取行动。
        + 《项目健康状态的三种颜色意味着什么？》: 用图表和案例解释绿色、黄色、红色背后的风险和应对策略。
- 知识卡片 (Knowledge Cards):
    * 形式: 可快速浏览的、信息高度浓缩的卡片。
    * 示例:
        + 《工作计划三步法速查表》: 一张卡片，清晰列出步骤1、2、3的目标、参与者和关键输出。
        + 《常见会议议程一览》: 一张可搜索的卡片，包含所有会议（如“计划进度文件审查会议”、“变更审核会议”）的议程、时长和邀请人清单。
        + 《评判标准速记》: 一张卡片，汇总“项目健康”、“任务健康”、“障碍健康”的评判标准。
- 高级功能与集成:
- 与工作流强关联 (Workflow-Integrated Learning):
    * 智能推荐: 当用户首次进入某个功能模块时，学习中心会自动弹出相关的学习内容。
        + 例如，当用户第一次点击“障碍管理”时，系统会弹出：“您是否需要学习《如何管理迫在眉睫的障碍》？”
    * 任务内嵌: 在复杂的操作步骤旁边，提供一个“学习此步骤”的链接，点击后直接跳转到对应的交互式向导或视频。
- “第一周”新手引导 (New User Onboarding - "First Week"):
    * 专属路径: 为新用户创建一个“第一周项目管理指南”专属学习路径。
    * 内容: 按照文档中的“第一周的项目管理指南”目录，将“首日活动”、“第一周活动”、“需要背诵的标准”等内容打包成一个7天的挑战。
    * 任务驱动: 每天解锁一个新任务，例如“Day 1: 完成首日任务清单”、“Day 2: 安排客户方法发现会议”等。完成学习后，用户才能在主平台上执行对应的操作。
- 知识测验与认证 (Quizzes & Certification):
    * 形式: 在每个学习模块后，提供一个简短的测验。
    * 目的: 确保用户真正掌握了关键知识。
    * 示例: 学习完“如何计算延迟日期”后，测验题可能是：“如果一个任务的时间裕量是5天，今天是10月10日，该任务因障碍而延期，那么它的延迟日期是哪一天？”
    * 认证: 用户完成所有核心模块的学习和测验后，可获得“PMP ProGuide 认证”徽章。
- 搜索与索引 (Search & Index):
    * 全文搜索: 用户可以搜索任何关键词（如“WBS”、“A&A表格”、“半场制”），学习中心会返回所有相关的视频、向导和知识卡片。
    * 主题索引: 按主题（如“规划”、“执行”、“监控”、“变更”）对内容进行分类索引。
通过以上深化设计，PMP ProGuide平台的“学习中心”不再是一个被动的知识仓库，而是一个主动的、交互式的、与工作紧密结合的“教练”和“陪练”，确保项目管理小白能够快速上手，并在实践中不断巩固和提升技能。

##PMP ProGuide平台 UI 设计

###设计原则 (Design Principles)

- 清晰优先 (Clarity First): 信息层级分明，避免视觉噪音。使用留白、字体大小和颜色来引导用户注意力。
- 一致性 (Consistency): 全平台采用统一的色彩、字体、图标和交互模式。
- 向导式体验 (Wizard-Driven Experience): 使用进度条、步骤指示器和清晰的“下一步”按钮，引导用户完成复杂流程。
- 情感化设计 (Emotional Design): 使用绿色、黄色、红色等健康状态颜色进行直观反馈，同时通过微交互（如任务完成的动画）给予用户正向激励。
- 响应式设计 (Responsive): 适配桌面、平板和手机端，确保随时随地都能管理项目。
###色彩与字体 (Color & Typography)

- ####主色调:
    * 健康绿色: #4CAF50 (用于成功状态、完成的任务)
    * 警示黄色: #FFC107 (用于警告、需要注意的任务)
    * 危险红色: #F44336 (用于错误、延期、高风险)
    * 主品牌色: #2196F3 (蓝色，用于主按钮、链接、高亮)
    * 中性色: #607D8B (灰色，用于文本、边框)
- ####字体:
    * 主标题: Roboto Bold, 24px
    * 副标题/按钮: Roboto Medium, 16px
    * 正文: Roboto Regular, 14px
    * 辅助文本: Roboto Light, 12px
###核心模块 UI 设计



####模块一：项目启动向导 (Project Kickoff Wizard)

整体布局: 采用全屏向导式布局，左侧为固定导航（项目启动向导），右侧为主要内容区。

#####功能1.1：首日任务清单 (Day 1 Checklist)

- 界面设计:
    * 标题: “第一天：启动您的项目” (居中，大号字体)
    * 进度条: 位于标题下方，一个水平的进度条显示 [■■■□□] 4/5，直观展示完成进度。
    * 任务列表:
        + 使用卡片式设计，每个任务为一个独立的卡片。
        + 未完成任务: 卡片背景为白色，复选框为空心，任务标题为深灰色。
        + 已完成任务: 卡片背景为浅绿色 (#E8F5E9)，复选框为实心绿色，任务标题为绿色。
    * 交互设计:
        + 点击任务卡片，会平滑展开，显示该任务的详细说明和操作按钮（如“开始采访”）。
        + 完成任务后，点击复选框，出现绿色对勾动画，卡片背景渐变为浅绿色，进度条自动更新。
    * 上下文帮助: 每个任务卡片右上角都有一个 ? 图标，点击后从右侧滑出帮助面板，显示相关文档片段。
#####功能1.2：客户方法发现会议 (Client Method Discovery Meeting)

- 界面设计:
    * 导航: 在“首日任务清单”完成后，用户点击“下一步”进入此模块。
    * 表单布局: 采用分步向导形式，顶部有步骤指示器 [1. 收集] → [2. 确认] → [3. 发布]。
    * 收集信息页:
        + 布局: 左右分栏。左侧为可勾选的“标准可交付成果”列表，右侧为动态填写区。
        + 风险提示: 当用户勾选一个可交付成果时，下方立即弹出一个红色警示条，要求用户确认。
    * 会议安排与纪要生成:
        + 提供一个醒目的“安排会议”大按钮（主品牌色）。
        + “生成纪要”按钮位于页面底部，生成后提供预览和发布选项。


####模块二：项目规划中心 (Project Planning Hub)

整体布局: 采用工作台（Workbench）布局，左侧为功能菜单，顶部为项目导航，中央为工作区。

#####功能2.1：工作计划构建器 (Work Plan Builder)

- 界面设计:
    * 向导进度条: 顶部一个清晰的横向进度条，显示 步骤1: 收集信息 → 步骤2: 团队评审 → 步骤3: 职能经理审核 → 最终审查。
    * 步骤1：收集信息
        + 会议安排区: 一个醒目的卡片，显示“第一步：安排与团队的会议”。包含“安排会议”按钮和会议议程预览。
        + 信息录入区: 会议后，显示一个表格，用于输入任务、工时、依赖关系等。字段旁有实时校验图标（对勾/叉号）。
        + “半场制”提示: 在工时输入框旁，用小字显示“例如：8小时 = 2天”。
    * 步骤2 & 3：评审与审核
        + 平台内协作: 会议邀请发出后，平台内会创建一个“评审区”，团队成员可以在此发表评论或确认。
        + 状态标记: 任务旁边显示“团队已确认”或“待确认”的标签。
#####功能2.2：计划进度表发布 (Schedule Release)

- 界面设计:
    * 流程看板: 采用垂直的、四阶段看板。
        + 阶段1：文件审查 - 包含“安排会议”按钮和“批准状态”指示器。
        + 阶段2：批准会议 - 包含“安排会议”按钮和一个显著的 ? 图标，点击后弹出“请使用WBS而非工作计划进行审查”的智能提示。
        + 阶段3：验收会议 - 包含“安排会议”按钮和“接受状态”列表。
        + 阶段4：最终归档 - 包含一个巨大的“发布最终计划”按钮。
    * WBS生成预览: 在“批准会议”阶段，提供一个WBS的PDF预览图，让用户确认。


####模块三：执行与监控面板 (Execution & Monitoring Dashboard)

整体布局: 采用信息仪表盘布局，以“健康状态仪表盘”为核心，其他信息围绕其展开。

#####功能3.1：健康状态仪表盘 (Health Status Dashboard)

- 界面设计:
    * 中心焦点: 一个巨大的、动态的交通灯式健康状态指示器（红/黄/绿），占据屏幕中央。
    * 环绕信息:
        + 左上角: “项目总览” - 显示计划 vs. 实际的进度条。
        + 右上角: “关键任务” - 一个列表，显示“时间裕量 <= 10天”的任务，背景为浅黄色。
        + 左下角: “迫在眉睫的障碍” - 一个高优先级列表，红色闪烁的警示条。
        + 右下角: “关键路径” - 一个简化的甘特图，关键路径任务高亮显示。
    * 交互设计: 点击任何一个区域，可以钻取到更详细的视图。
#####功能3.2：每日/每周状态报告

- 界面设计:
    * 每日状态报告 (DSR):
        + 三栏式布局: 清晰的三个区域，分别对应“三个感恩”、“今天的成就”、“明天的计划成就”。
        + 成就引导: 在“今天的成就”区域，提供“从日历导入”按钮和“成就检查”提示（如输入“参加会议”时弹出提示）。
    * 每周项目状态报告:
        + 向导式模板: 用户按顺序填写每个部分。
        + 自动化填充: “项目运行状况”区域直接显示来自仪表盘的健康状态，用户无法编辑。
        + 一键导出: “当前和下一个时期计划任务”区域提供“导出为图片”按钮，点击后自动生成并插入图片。
#####功能3.3：障碍管理 (Obstacle Management)

- 界面设计:
    * 障碍看板 (Kanban): 采用经典的看板布局，五列：“待处理”、“迫在眉睫”、“已评估”、“已批准”、“已解决”。
    * 障碍卡片:
        + 显示障碍名称、延迟日期、接受任务者。
        + 背景颜色根据健康状态自动变化（红/黄/绿）。
        + “迫在眉睫”的障碍卡片有红色边框和闪烁动画。
    * “10天倒计时”集成: 在“迫在眉睫”列的标题旁，显示一个总的倒计时：“距离截止日：7天”。
#####功能3.4：会议中心 (Meeting Hub)

- 界面设计:
    * 主视图: 一个集成的日历视图（月/周/日）。
    * 会议创建:
        + 点击“+新建会议”，弹出一个模态窗口。
        + 会议类型选择: 一个下拉菜单或图标网格，用户选择会议类型后，时长、议程等自动填充。
        + 议程预览: 窗口下方实时显示生成的议程表格。
    * 会议详情页:
        + 包含会议信息、议程、参会人列表。
        + 会前: 显示“文件已发送”状态。
        + 会后: 包含“生成纪要”按钮和“实时笔记”输入区。


####模块四：变更管理流程 (Change Management Workflow)

功能4.1 & 4.2：变更请求与10天倒计时提醒

- 界面设计:
    * 变更请求表单: 一个简洁的表单，突出“变更类型”和“预期完成日期”。
    * 10天倒计时提醒:
        + 任务面板: 在用户的个人任务列表顶部，有一个不可忽略的红色提醒卡片，显示“迫在眉睫！ 请在[7]天内完成‘项目变更评估’”。
        + 每日状态报告集成: 在用户填写DSR时，“明天的计划成就”上方出现黄色提示条：“注意：您有1个‘项目变更评估’需要在7天内完成”。


####模块五：知识库与帮助中心 (Knowledge Base & Help Center)

#####功能5.1：上下文帮助 (Contextual Help)

- 实现:
    * “？”按钮: 在每个功能模块的右上角，一个蓝色的 ? 图标。
    * 帮助面板: 点击后，从右侧滑出一个半透明的面板，显示与当前操作直接相关的“评判标准”、“设计目标”和“要点精炼”。
    * 高亮显示: “要点精炼”部分用黄色背景突出。
#####功能5.2：学习中心 (Learning Center)

- 界面设计:
    * 主页: 一个类似视频平台的网格布局，展示“交互式向导”、“短视频”和“知识卡片”。
    * “第一周”新手引导: 为新用户创建一个专属的挑战路径，显示“Day 1/7”进度。
    * 学习路径: 对于复杂流程，提供“开始学习”按钮，引导用户完成一系列微课程。
这套UI设计方案将PMP ProGuide平台的每一个功能都可视化、向导化和情感化。通过清晰的视觉层次、智能的上下文帮助和强制性的流程引导，即使是项目管理小白，也能在直观的界面指引下，像专家一样完成每一个复杂的管理动作，最终实现“确保每个项目获得一致的成功结果”的终极目标。



