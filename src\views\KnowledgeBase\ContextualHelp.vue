<template>
  <div class="contextual-help">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><QuestionFilled /></el-icon>
        上下文帮助
      </h1>
    </div>

    <div class="help-content">
      <el-card>
        <template #header>
          <h3>{{ helpData.title }}</h3>
        </template>
        
        <div class="help-sections">
          <div class="section">
            <h4>核心原则</h4>
            <ul>
              <li v-for="principle in helpData.principles" :key="principle">
                {{ principle }}
              </li>
            </ul>
          </div>
          
          <div class="section">
            <h4>关键标准</h4>
            <ul>
              <li v-for="criterion in helpData.criteria" :key="criterion">
                {{ criterion }}
              </li>
            </ul>
          </div>
          
          <div class="section">
            <h4>要点提醒</h4>
            <ul>
              <li v-for="point in helpData.keyPoints" :key="point">
                {{ point }}
              </li>
            </ul>
          </div>
          
          <div class="section">
            <h4>操作指南</h4>
            <ol>
              <li v-for="guide in helpData.actionGuide" :key="guide">
                {{ guide }}
              </li>
            </ol>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { knowledgeBaseApi } from '@/api/pmpApi'

const helpData = ref({
  title: '',
  principles: [],
  criteria: [],
  keyPoints: [],
  actionGuide: []
})

onMounted(async () => {
  const response = await knowledgeBaseApi.getContextualHelp('work-plan-step1')
  if (response.code === 200) {
    helpData.value = response.data
  }
})
</script>

<style scoped>
.contextual-help {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.help-content {
  max-width: 800px;
  margin: 0 auto;
}

.help-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section h4 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 16px;
}

.section ul, .section ol {
  margin: 0;
  padding-left: 20px;
}

.section li {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}
</style>
