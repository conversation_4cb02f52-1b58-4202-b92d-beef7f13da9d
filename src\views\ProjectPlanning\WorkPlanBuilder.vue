<template>
  <div class="work-plan-builder">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          工作计划构建器
        </h1>
        <div class="step-indicator">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="收集信息" description="从团队收集任务信息" />
            <el-step title="团队评审" description="与团队确认计划准确性" />
            <el-step title="职能经理审核" description="获得经理层面认可" />
            <el-step title="最终审查" description="完成计划批准" />
          </el-steps>
        </div>
      </div>
    </div>

    <div class="builder-content">
      <!-- 步骤1: 收集信息 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-card class="step-card">
          <template #header>
            <div class="card-header">
              <h3>步骤1：收集信息</h3>
              <el-button type="info" size="small" circle @click="showHelp('step1')">
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="step1-content">
            <el-alert 
              title="重要提醒" 
              type="warning" 
              :closable="false"
              show-icon
            >
              此步骤的目标是收集信息，而非创建计划。请勿在会议中打开MS Project。
            </el-alert>

            <div class="meeting-section">
              <h4>安排收集信息会议</h4>
              <p class="section-desc">与领域专家（团队成员）安排会议，收集项目任务信息。</p>
              
              <el-form :model="meetingForm" label-width="120px">
                <el-form-item label="会议主题">
                  <el-input 
                    v-model="meetingForm.subject" 
                    placeholder="[项目名称]:工作计划步骤1会议"
                  />
                </el-form-item>
                
                <el-form-item label="参会人员">
                  <el-select 
                    v-model="meetingForm.attendees" 
                    multiple 
                    placeholder="选择领域专家（团队成员）"
                    style="width: 100%"
                  >
                    <el-option label="张三 - 前端开发" value="zhangsan" />
                    <el-option label="李四 - 后端开发" value="lisi" />
                    <el-option label="王五 - 测试工程师" value="wangwu" />
                    <el-option label="赵六 - UI设计师" value="zhaoliu" />
                  </el-select>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="scheduleMeeting">
                    <el-icon><Calendar /></el-icon>
                    安排会议
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="task-collection" v-if="meetingScheduled">
              <h4>任务信息收集</h4>
              <p class="section-desc">会议后，请在此录入从团队成员处收集到的信息。</p>
              
              <div class="task-form">
                <el-button type="success" @click="addTask" style="margin-bottom: 16px">
                  <el-icon><Plus /></el-icon>
                  添加任务
                </el-button>
                
                <div class="task-list">
                  <div 
                    v-for="(task, index) in tasks" 
                    :key="index"
                    class="task-item"
                  >
                    <el-card>
                      <div class="task-fields">
                        <el-form-item label="任务名称">
                          <el-input 
                            v-model="task.name" 
                            placeholder="请输入任务名称（必须包含动词）"
                          />
                        </el-form-item>
                        
                        <el-form-item label="工作量(小时)">
                          <el-input-number 
                            v-model="task.workHours" 
                            :min="1" 
                            :max="200"
                            placeholder="工时"
                          />
                          <span class="duration-hint">
                            ≈ {{ Math.ceil(task.workHours / 4) }}天 (半场制)
                          </span>
                        </el-form-item>
                        
                        <el-form-item label="负责人">
                          <el-select v-model="task.assignee" placeholder="选择负责人">
                            <el-option label="张三" value="zhangsan" />
                            <el-option label="李四" value="lisi" />
                            <el-option label="王五" value="wangwu" />
                            <el-option label="赵六" value="zhaoliu" />
                          </el-select>
                        </el-form-item>
                        
                        <el-form-item label="前置任务">
                          <el-select 
                            v-model="task.dependencies" 
                            multiple 
                            placeholder="选择前置任务"
                          >
                            <el-option 
                              v-for="(otherTask, otherIndex) in tasks" 
                              :key="otherIndex"
                              :label="otherTask.name || `任务${otherIndex + 1}`"
                              :value="otherIndex"
                              :disabled="otherIndex === index"
                            />
                          </el-select>
                        </el-form-item>
                        
                        <div class="task-actions">
                          <el-button 
                            type="danger" 
                            size="small" 
                            @click="removeTask(index)"
                          >
                            删除
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button 
            type="primary" 
            size="large" 
            @click="generateWorkPlan"
            :disabled="!canGeneratePlan"
          >
            生成工作计划
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 团队评审 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-card class="step-card">
          <template #header>
            <h3>步骤2：团队评审</h3>
          </template>
          
          <div class="step2-content">
            <el-alert 
              title="评审目标" 
              type="info" 
              :closable="false"
              show-icon
            >
              与团队成员确认工作计划的准确性，确保信息正确无误。请勿在会议中直接修改MS Project文件。
            </el-alert>

            <div class="plan-preview">
              <h4>工作计划预览</h4>
              <el-table :data="tasks" border style="width: 100%">
                <el-table-column prop="name" label="任务名称" />
                <el-table-column prop="workHours" label="工时" width="80" />
                <el-table-column label="持续时间" width="100">
                  <template #default="scope">
                    {{ Math.ceil(scope.row.workHours / 4) }}天
                  </template>
                </el-table-column>
                <el-table-column prop="assignee" label="负责人" width="120" />
                <el-table-column label="状态" width="100">
                  <template #default="scope">
                    <el-tag 
                      :type="scope.row.reviewed ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ scope.row.reviewed ? '已确认' : '待确认' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button 
                      v-if="!scope.row.reviewed"
                      type="success" 
                      size="small"
                      @click="confirmTask(scope.$index)"
                    >
                      确认
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="review-actions">
              <el-button type="primary" @click="scheduleReviewMeeting">
                <el-icon><Calendar /></el-icon>
                安排评审会议
              </el-button>
              <el-button 
                type="success" 
                @click="completeReview"
                :disabled="!allTasksReviewed"
              >
                完成评审
              </el-button>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button size="large" @click="prevStep">
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="nextStep"
            :disabled="!allTasksReviewed"
          >
            下一步：职能经理审核
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤3: 职能经理审核 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-card class="step-card">
          <template #header>
            <h3>步骤3：职能经理审核</h3>
          </template>
          
          <div class="step3-content">
            <el-alert 
              title="审核目标" 
              type="info" 
              :closable="false"
              show-icon
            >
              获得职能经理对其团队成员工作量和进度的最终认可。
            </el-alert>

            <div class="manager-review">
              <h4>职能经理审核状态</h4>
              <div class="manager-list">
                <div 
                  v-for="manager in managers" 
                  :key="manager.id"
                  class="manager-item"
                >
                  <div class="manager-info">
                    <h5>{{ manager.name }}</h5>
                    <p>负责团队：{{ manager.team.join(', ') }}</p>
                  </div>
                  <div class="manager-status">
                    <el-tag 
                      :type="manager.approved ? 'success' : 'warning'"
                      size="large"
                    >
                      {{ manager.approved ? '已批准' : '待批准' }}
                    </el-tag>
                  </div>
                  <div class="manager-actions">
                    <el-button 
                      v-if="!manager.approved"
                      type="primary" 
                      size="small"
                      @click="requestApproval(manager)"
                    >
                      请求批准
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="approval-actions">
              <el-button type="primary" @click="scheduleManagerMeeting">
                <el-icon><Calendar /></el-icon>
                安排审核会议
              </el-button>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button size="large" @click="prevStep">
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="nextStep"
            :disabled="!allManagersApproved"
          >
            下一步：最终审查
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤4: 最终审查 -->
      <div v-if="currentStep === 3" class="step-content">
        <el-result
          icon="success"
          title="工作计划构建完成"
          sub-title="所有步骤已完成，工作计划已准备好进入发布流程"
        >
          <template #extra>
            <el-button type="primary" size="large" @click="goToScheduleRelease">
              进入计划进度表发布
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, QuestionFilled, Calendar, Plus, ArrowRight, ArrowLeft 
} from '@element-plus/icons-vue'
import { projectPlanningApi } from '@/api/pmpApi'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const meetingScheduled = ref(false)
const meetingForm = ref({
  subject: '[项目名称]:工作计划步骤1会议',
  attendees: []
})
const tasks = ref([])
const managers = ref([
  { id: 1, name: '技术经理', team: ['张三', '李四'], approved: false },
  { id: 2, name: '测试经理', team: ['王五'], approved: false },
  { id: 3, name: '设计经理', team: ['赵六'], approved: false }
])

// 计算属性
const canGeneratePlan = computed(() => {
  return meetingScheduled.value && tasks.value.length > 0 && 
         tasks.value.every(task => task.name && task.workHours && task.assignee)
})

const allTasksReviewed = computed(() => {
  return tasks.value.length > 0 && tasks.value.every(task => task.reviewed)
})

const allManagersApproved = computed(() => {
  return managers.value.every(manager => manager.approved)
})

// 页面挂载时加载数据
onMounted(() => {
  loadWorkPlanData()
})

// 加载工作计划数据
const loadWorkPlanData = async () => {
  try {
    const response = await projectPlanningApi.getWorkPlanBuilder()
    if (response.code === 200) {
      currentStep.value = response.data.currentStep - 1
      if (response.data.tasks.length > 0) {
        tasks.value = response.data.tasks.map(task => ({
          ...task,
          reviewed: false
        }))
        meetingScheduled.value = true
      }
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 安排会议
const scheduleMeeting = () => {
  if (meetingForm.value.attendees.length === 0) {
    ElMessage.warning('请选择参会人员')
    return
  }
  
  ElMessage.success('会议已安排，邮件邀请已发送')
  meetingScheduled.value = true
}

// 添加任务
const addTask = () => {
  tasks.value.push({
    name: '',
    workHours: 8,
    assignee: '',
    dependencies: [],
    reviewed: false
  })
}

// 删除任务
const removeTask = (index) => {
  tasks.value.splice(index, 1)
}

// 生成工作计划
const generateWorkPlan = () => {
  ElMessage.success('工作计划已生成！')
  currentStep.value = 1
}

// 确认任务
const confirmTask = (index) => {
  tasks.value[index].reviewed = true
  ElMessage.success('任务已确认')
}

// 安排评审会议
const scheduleReviewMeeting = () => {
  ElMessage.success('评审会议已安排')
}

// 完成评审
const completeReview = () => {
  ElMessage.success('团队评审已完成')
}

// 请求批准
const requestApproval = (manager) => {
  ElMessageBox.confirm(
    `确定要向${manager.name}请求批准吗？`,
    '请求批准',
    {
      confirmButtonText: '发送请求',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    manager.approved = true
    ElMessage.success(`已向${manager.name}发送批准请求`)
  })
}

// 安排经理会议
const scheduleManagerMeeting = () => {
  ElMessage.success('职能经理审核会议已安排')
}

// 显示帮助
const showHelp = (type) => {
  let content = ''
  if (type === 'step1') {
    content = '此步骤的目标是秘密地从领域专家那里收集信息，而不在他们面前创建或修改工作计划。'
  }
  ElMessageBox.alert(content, '帮助信息')
}

// 步骤导航
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 进入进度表发布
const goToScheduleRelease = () => {
  router.push('/project-planning/schedule-release')
}
</script>

<style scoped>
.work-plan-builder {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 24px 0;
}

.builder-content {
  max-width: 1000px;
  margin: 0 auto;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.section-desc {
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.5;
}

.meeting-section, .task-collection {
  margin-top: 24px;
}

.meeting-section h4, .task-collection h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-item {
  border-left: 4px solid #409eff;
}

.task-fields {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 16px;
  align-items: end;
}

.duration-hint {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.plan-preview {
  margin: 24px 0;
}

.plan-preview h4 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.review-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 24px;
}

.manager-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.manager-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.manager-info h5 {
  margin: 0 0 4px 0;
  color: #2c3e50;
}

.manager-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.approval-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
}

@media (max-width: 768px) {
  .task-fields {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .manager-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
