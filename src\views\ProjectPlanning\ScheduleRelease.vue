<template>
  <div class="schedule-release">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Calendar /></el-icon>
          计划进度表发布
        </h1>
        <div class="phase-indicator">
          <el-steps :active="currentPhase" finish-status="success" align-center>
            <el-step title="文件审查" description="项目经理质量保证" />
            <el-step title="批准会议" description="高层正式批准" />
            <el-step title="验收会议" description="团队接受确认" />
            <el-step title="最终归档" description="发布执行基准" />
          </el-steps>
        </div>
      </div>
    </div>

    <div class="release-content">
      <!-- 阶段1: 文件审查 -->
      <div v-if="currentPhase === 0" class="phase-content">
        <el-card class="phase-card">
          <template #header>
            <div class="card-header">
              <h3>阶段1：文件审查</h3>
              <el-button type="info" size="small" circle @click="showHelp('review')">
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="review-content">
            <el-alert 
              title="审查目标" 
              type="info" 
              :closable="false"
              show-icon
            >
              获得项目经理（上级）的内部质量保证和批准，确保计划的完整性和合理性。
            </el-alert>

            <div class="document-preparation">
              <h4>文件准备状态</h4>
              <div class="document-list">
                <div 
                  v-for="doc in documents" 
                  :key="doc.name"
                  class="document-item"
                  :class="{ 'ready': doc.status === 'ready' }"
                >
                  <div class="doc-info">
                    <el-icon><Document /></el-icon>
                    <span class="doc-name">{{ doc.name }}</span>
                    <el-tag 
                      :type="getDocStatusType(doc.status)" 
                      size="small"
                    >
                      {{ getDocStatusText(doc.status) }}
                    </el-tag>
                  </div>
                  <div class="doc-actions">
                    <el-button 
                      v-if="doc.status === 'generating'"
                      type="primary" 
                      size="small"
                      @click="generateDocument(doc)"
                    >
                      生成
                    </el-button>
                    <el-button 
                      v-if="doc.status === 'ready'"
                      type="success" 
                      size="small"
                      @click="previewDocument(doc)"
                    >
                      预览
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="meeting-arrangement">
              <h4>安排文件审查会议</h4>
              <el-form :model="reviewMeetingForm" label-width="120px">
                <el-form-item label="会议主题">
                  <el-input 
                    v-model="reviewMeetingForm.subject" 
                    placeholder="[项目名称]:计划文件审查会议"
                  />
                </el-form-item>
                
                <el-form-item label="邀请人">
                  <el-input 
                    v-model="reviewMeetingForm.invitee" 
                    placeholder="项目经理"
                    readonly
                  />
                </el-form-item>
                
                <el-form-item label="会议时间">
                  <el-date-picker
                    v-model="reviewMeetingForm.datetime"
                    type="datetime"
                    placeholder="选择会议时间"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="scheduleReviewMeeting"
                    :disabled="!allDocumentsReady"
                  >
                    <el-icon><Calendar /></el-icon>
                    安排会议
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="approval-status" v-if="reviewMeetingScheduled">
              <h4>审查状态</h4>
              <div class="status-item">
                <el-icon><SuccessFilled /></el-icon>
                <span>会议已安排</span>
                <el-tag type="success" size="small">完成</el-tag>
              </div>
              <div class="status-item">
                <el-icon><Document /></el-icon>
                <span>文件已发送</span>
                <el-tag 
                  :type="filesSent ? 'success' : 'warning'" 
                  size="small"
                >
                  {{ filesSent ? '已发送' : '待发送' }}
                </el-tag>
              </div>
              <div class="status-item">
                <el-icon><Check /></el-icon>
                <span>项目经理批准</span>
                <el-tag 
                  :type="managerApproved ? 'success' : 'info'" 
                  size="small"
                >
                  {{ managerApproved ? '已批准' : '待批准' }}
                </el-tag>
              </div>
              
              <div class="approval-actions">
                <el-button 
                  v-if="!filesSent"
                  type="primary" 
                  @click="sendFiles"
                >
                  发送文件
                </el-button>
                <el-button 
                  v-if="filesSent && !managerApproved"
                  type="success" 
                  @click="requestApproval"
                >
                  请求电子批准
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <div class="phase-actions">
          <el-button 
            type="primary" 
            size="large" 
            @click="nextPhase"
            :disabled="!managerApproved"
          >
            下一阶段：批准会议
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 阶段2: 批准会议 -->
      <div v-if="currentPhase === 1" class="phase-content">
        <el-card class="phase-card">
          <template #header>
            <h3>阶段2：批准会议</h3>
          </template>
          
          <div class="approval-content">
            <el-alert 
              title="重要提醒" 
              type="warning" 
              :closable="false"
              show-icon
            >
              此会议是获得项目负责人和高层支持的关键。请使用WBS进行审查，而非详细的工作计划。
            </el-alert>

            <div class="wbs-preview">
              <h4>WBS预览</h4>
              <p class="preview-desc">工作计划过于详细，不适合向高层汇报。WBS提供了合适的详细程度。</p>
              <div class="wbs-placeholder">
                <el-icon size="48"><DataBoard /></el-icon>
                <p>WBS文档预览</p>
                <el-button type="primary" @click="generateWBS">生成WBS PDF</el-button>
              </div>
            </div>

            <div class="approval-meeting">
              <h4>安排批准会议</h4>
              <el-form :model="approvalMeetingForm" label-width="120px">
                <el-form-item label="会议主题">
                  <el-input 
                    v-model="approvalMeetingForm.subject" 
                    placeholder="[项目名称]:计划文件批准会议"
                  />
                </el-form-item>
                
                <el-form-item label="邀请人">
                  <el-select 
                    v-model="approvalMeetingForm.invitees" 
                    multiple 
                    placeholder="选择高层管理者"
                    style="width: 100%"
                  >
                    <el-option label="项目负责人" value="sponsor" />
                    <el-option label="部门总监" value="director" />
                    <el-option label="技术总监" value="cto" />
                  </el-select>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="scheduleApprovalMeeting">
                    <el-icon><Calendar /></el-icon>
                    安排批准会议
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="stakeholder-approval" v-if="approvalMeetingScheduled">
              <h4>利益相关者批准状态</h4>
              <div class="stakeholder-list">
                <div 
                  v-for="stakeholder in stakeholders" 
                  :key="stakeholder.id"
                  class="stakeholder-item"
                >
                  <div class="stakeholder-info">
                    <h5>{{ stakeholder.name }}</h5>
                    <p>{{ stakeholder.role }}</p>
                  </div>
                  <div class="stakeholder-status">
                    <el-tag 
                      :type="stakeholder.approved ? 'success' : 'warning'"
                      size="large"
                    >
                      {{ stakeholder.approved ? '已批准' : '待批准' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <div class="phase-actions">
          <el-button size="large" @click="prevPhase">
            <el-icon><ArrowLeft /></el-icon>
            上一阶段
          </el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="nextPhase"
            :disabled="!allStakeholdersApproved"
          >
            下一阶段：验收会议
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 阶段3: 验收会议 -->
      <div v-if="currentPhase === 2" class="phase-content">
        <el-card class="phase-card">
          <template #header>
            <h3>阶段3：验收会议</h3>
          </template>
          
          <div class="acceptance-content">
            <el-alert 
              title="验收目标" 
              type="info" 
              :closable="false"
              show-icon
            >
              确保执行团队（团队成员及其职能经理）理解并接受他们的任务分配。
            </el-alert>

            <div class="team-acceptance">
              <h4>团队接受状态</h4>
              <div class="team-list">
                <div 
                  v-for="member in teamMembers" 
                  :key="member.id"
                  class="team-member"
                >
                  <div class="member-info">
                    <h5>{{ member.name }}</h5>
                    <p>{{ member.role }} - {{ member.tasks }}个任务</p>
                  </div>
                  <div class="member-status">
                    <el-tag 
                      :type="member.accepted ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ member.accepted ? '已接受' : '待接受' }}
                    </el-tag>
                  </div>
                  <div class="member-actions">
                    <el-button 
                      v-if="!member.accepted"
                      type="primary" 
                      size="small"
                      @click="requestAcceptance(member)"
                    >
                      请求接受
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="acceptance-meeting">
              <el-button type="primary" @click="scheduleAcceptanceMeeting">
                <el-icon><Calendar /></el-icon>
                安排验收会议
              </el-button>
            </div>
          </div>
        </el-card>

        <div class="phase-actions">
          <el-button size="large" @click="prevPhase">
            <el-icon><ArrowLeft /></el-icon>
            上一阶段
          </el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="nextPhase"
            :disabled="!allMembersAccepted"
          >
            下一阶段：最终归档
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 阶段4: 最终归档 -->
      <div v-if="currentPhase === 3" class="phase-content">
        <el-result
          icon="success"
          title="计划进度表发布完成"
          sub-title="所有批准和接受已完成，项目执行基准已建立"
        >
          <template #extra>
            <el-button type="primary" size="large" @click="goToExecution">
              进入执行与监控面板
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Calendar, QuestionFilled, Document, SuccessFilled, 
  Check, ArrowRight, ArrowLeft, DataBoard 
} from '@element-plus/icons-vue'
import { projectPlanningApi } from '@/api/pmpApi'

const router = useRouter()

// 响应式数据
const currentPhase = ref(0)
const documents = ref([])
const reviewMeetingForm = ref({
  subject: '[项目名称]:计划文件审查会议',
  invitee: '项目经理',
  datetime: ''
})
const approvalMeetingForm = ref({
  subject: '[项目名称]:计划文件批准会议',
  invitees: []
})
const reviewMeetingScheduled = ref(false)
const approvalMeetingScheduled = ref(false)
const filesSent = ref(false)
const managerApproved = ref(false)
const stakeholders = ref([
  { id: 1, name: '项目负责人', role: '项目发起人', approved: false },
  { id: 2, name: '部门总监', role: '业务负责人', approved: false }
])
const teamMembers = ref([
  { id: 1, name: '张三', role: '前端开发', tasks: 5, accepted: false },
  { id: 2, name: '李四', role: '后端开发', tasks: 8, accepted: false },
  { id: 3, name: '王五', role: '测试工程师', tasks: 3, accepted: false }
])

// 计算属性
const allDocumentsReady = computed(() => {
  return documents.value.every(doc => doc.status === 'ready')
})

const allStakeholdersApproved = computed(() => {
  return stakeholders.value.every(s => s.approved)
})

const allMembersAccepted = computed(() => {
  return teamMembers.value.every(m => m.accepted)
})

// 页面挂载时加载数据
onMounted(() => {
  loadScheduleData()
})

// 加载进度表数据
const loadScheduleData = async () => {
  try {
    const response = await projectPlanningApi.getScheduleRelease()
    if (response.code === 200) {
      currentPhase.value = response.data.currentPhase
      documents.value = response.data.documents
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 获取文档状态类型
const getDocStatusType = (status) => {
  const types = {
    ready: 'success',
    generating: 'warning',
    error: 'danger'
  }
  return types[status] || 'info'
}

// 获取文档状态文本
const getDocStatusText = (status) => {
  const texts = {
    ready: '就绪',
    generating: '生成中',
    error: '错误'
  }
  return texts[status] || '未知'
}

// 生成文档
const generateDocument = (doc) => {
  doc.status = 'ready'
  ElMessage.success(`${doc.name}生成完成`)
}

// 预览文档
const previewDocument = (doc) => {
  ElMessage.info(`正在预览${doc.name}...`)
}

// 安排审查会议
const scheduleReviewMeeting = () => {
  if (!reviewMeetingForm.value.datetime) {
    ElMessage.warning('请选择会议时间')
    return
  }
  
  reviewMeetingScheduled.value = true
  ElMessage.success('文件审查会议已安排')
}

// 发送文件
const sendFiles = () => {
  filesSent.value = true
  ElMessage.success('文件已发送给项目经理')
}

// 请求批准
const requestApproval = () => {
  ElMessageBox.confirm(
    '确定要向项目经理请求电子批准吗？',
    '请求批准',
    {
      confirmButtonText: '发送请求',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    managerApproved.value = true
    ElMessage.success('项目经理已批准')
  })
}

// 生成WBS
const generateWBS = () => {
  ElMessage.success('WBS PDF已生成')
}

// 安排批准会议
const scheduleApprovalMeeting = () => {
  if (approvalMeetingForm.value.invitees.length === 0) {
    ElMessage.warning('请选择邀请人')
    return
  }
  
  approvalMeetingScheduled.value = true
  // 模拟批准
  setTimeout(() => {
    stakeholders.value.forEach(s => s.approved = true)
    ElMessage.success('高层管理者已批准')
  }, 2000)
}

// 请求接受
const requestAcceptance = (member) => {
  member.accepted = true
  ElMessage.success(`${member.name}已接受任务分配`)
}

// 安排验收会议
const scheduleAcceptanceMeeting = () => {
  ElMessage.success('验收会议已安排')
}

// 显示帮助
const showHelp = (type) => {
  let content = ''
  if (type === 'review') {
    content = '此会议是获得项目经理对计划文件进行质量保证的关键步骤。'
  }
  ElMessageBox.alert(content, '帮助信息')
}

// 阶段导航
const nextPhase = () => {
  if (currentPhase.value < 3) {
    currentPhase.value++
  }
}

const prevPhase = () => {
  if (currentPhase.value > 0) {
    currentPhase.value--
  }
}

// 进入执行监控
const goToExecution = () => {
  router.push('/execution-monitoring')
}
</script>

<style scoped>
.schedule-release {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 24px 0;
}

.release-content {
  max-width: 1000px;
  margin: 0 auto;
}

.phase-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.phase-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.document-preparation, .meeting-arrangement, .approval-status {
  margin: 24px 0;
}

.document-preparation h4, .meeting-arrangement h4, .approval-status h4 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #dcdfe6;
}

.document-item.ready {
  border-left-color: #67c23a;
}

.doc-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doc-name {
  font-weight: 500;
  color: #2c3e50;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
}

.approval-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.wbs-preview {
  margin: 24px 0;
}

.preview-desc {
  color: #606266;
  margin-bottom: 16px;
}

.wbs-placeholder {
  text-align: center;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dcdfe6;
}

.wbs-placeholder p {
  margin: 16px 0;
  color: #606266;
}

.stakeholder-list, .team-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stakeholder-item, .team-member {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.stakeholder-info h5, .member-info h5 {
  margin: 0 0 4px 0;
  color: #2c3e50;
}

.stakeholder-info p, .member-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.phase-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
}

@media (max-width: 768px) {
  .document-item, .stakeholder-item, .team-member {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .phase-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
