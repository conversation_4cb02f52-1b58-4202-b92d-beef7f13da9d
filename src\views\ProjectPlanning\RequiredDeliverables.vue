<template>
  <div class="required-deliverables">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Files /></el-icon>
          必需可交付成果确定
        </h1>
        <div class="step-indicator">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="文件审查" description="与项目经理确定范围" />
            <el-step title="文件批准" description="获得正式批准" />
          </el-steps>
        </div>
      </div>
    </div>

    <div class="deliverables-content">
      <!-- 步骤1: 文件审查 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-card class="step-card">
          <template #header>
            <div class="card-header">
              <h3>步骤1：文件审查</h3>
              <el-button type="info" size="small" circle @click="showHelp('review')">
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </div>
          </template>
          
          <div class="review-content">
            <el-alert 
              title="审查目标" 
              type="info" 
              :closable="false"
              show-icon
            >
              与项目经理合作，审查并确定项目需要哪些标准可交付成果。请在计划进度确定会之后安排。
            </el-alert>

            <div class="standard-deliverables">
              <h4>标准可交付成果</h4>
              <p class="section-desc">请与项目经理讨论，确定哪些标准可交付成果可以省略。</p>
              
              <div class="deliverables-list">
                <div 
                  v-for="deliverable in standardDeliverables" 
                  :key="deliverable.id"
                  class="deliverable-item"
                  :class="{ 'excluded': deliverable.excluded }"
                >
                  <div class="deliverable-header">
                    <div class="deliverable-info">
                      <el-checkbox 
                        v-model="deliverable.excluded"
                        @change="onDeliverableExclude(deliverable)"
                      >
                        排除此可交付成果
                      </el-checkbox>
                      <h5 class="deliverable-name">{{ deliverable.name }}</h5>
                    </div>
                    <el-tag 
                      :type="deliverable.required ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ deliverable.required ? '必需' : '可选' }}
                    </el-tag>
                  </div>
                  
                  <div v-if="deliverable.excluded" class="exclusion-reason">
                    <el-input
                      v-model="deliverable.reason"
                      type="textarea"
                      :rows="2"
                      placeholder="请说明排除此可交付成果的原因..."
                      maxlength="200"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="custom-deliverables">
              <h4>客户特定可交付成果</h4>
              <p class="section-desc">请列出所有客户要求的、超出标准范围的必需交付物。</p>
              
              <div class="custom-list">
                <div 
                  v-for="(custom, index) in customDeliverables" 
                  :key="index"
                  class="custom-item"
                >
                  <el-card>
                    <div class="custom-fields">
                      <el-form-item label="交付物名称">
                        <el-input 
                          v-model="custom.name" 
                          placeholder="例如：安全合规报告"
                        />
                      </el-form-item>
                      
                      <el-form-item label="描述">
                        <el-input
                          v-model="custom.description"
                          type="textarea"
                          :rows="2"
                          placeholder="请描述此交付物的具体要求..."
                        />
                      </el-form-item>
                      
                      <div class="custom-actions">
                        <el-button 
                          type="danger" 
                          size="small" 
                          @click="removeCustomDeliverable(index)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </el-card>
                </div>
                
                <el-button 
                  type="success" 
                  @click="addCustomDeliverable"
                  style="width: 100%"
                >
                  <el-icon><Plus /></el-icon>
                  添加客户特定可交付成果
                </el-button>
              </div>
            </div>

            <div class="meeting-arrangement">
              <h4>安排审查会议</h4>
              <el-form :model="reviewMeetingForm" label-width="120px">
                <el-form-item label="会议主题">
                  <el-input 
                    v-model="reviewMeetingForm.subject" 
                    placeholder="[项目名称]:必需的项目管理可交付成果文档审查"
                  />
                </el-form-item>
                
                <el-form-item label="邀请人">
                  <el-input 
                    v-model="reviewMeetingForm.invitee" 
                    placeholder="项目经理"
                    readonly
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="scheduleReviewMeeting">
                    <el-icon><Calendar /></el-icon>
                    安排审查会议
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button 
            type="primary" 
            size="large" 
            @click="generateDraft"
            :disabled="!reviewMeetingScheduled"
          >
            生成文档草案
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 文件批准 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-card class="step-card">
          <template #header>
            <h3>步骤2：文件批准</h3>
          </template>
          
          <div class="approval-content">
            <el-alert 
              title="批准目标" 
              type="info" 
              :closable="false"
              show-icon
            >
              此会议的唯一目标是获得项目经理对"必需的项目管理可交付成果"文档的口头批准。
            </el-alert>

            <div class="document-summary">
              <h4>文档摘要</h4>
              <div class="summary-content">
                <div class="included-deliverables">
                  <h5>包含的标准可交付成果</h5>
                  <ul>
                    <li 
                      v-for="deliverable in includedDeliverables" 
                      :key="deliverable.id"
                    >
                      {{ deliverable.name }}
                    </li>
                  </ul>
                </div>
                
                <div class="excluded-deliverables" v-if="excludedDeliverables.length > 0">
                  <h5>排除的标准可交付成果</h5>
                  <div class="excluded-list">
                    <div 
                      v-for="deliverable in excludedDeliverables" 
                      :key="deliverable.id"
                      class="excluded-item"
                    >
                      <span class="excluded-name">{{ deliverable.name }}</span>
                      <span class="excluded-reason">原因：{{ deliverable.reason }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="custom-deliverables-summary" v-if="customDeliverables.length > 0">
                  <h5>客户特定可交付成果</h5>
                  <ul>
                    <li 
                      v-for="custom in customDeliverables" 
                      :key="custom.name"
                    >
                      <strong>{{ custom.name }}</strong> - {{ custom.description }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="approval-meeting">
              <h4>安排批准会议</h4>
              <el-form :model="approvalMeetingForm" label-width="120px">
                <el-form-item label="会议主题">
                  <el-input 
                    v-model="approvalMeetingForm.subject" 
                    placeholder="[项目名称]:必需的项目管理可交付成果文件批准"
                  />
                </el-form-item>
                
                <el-form-item label="邀请人">
                  <el-input 
                    v-model="approvalMeetingForm.invitee" 
                    placeholder="项目经理"
                    readonly
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="scheduleApprovalMeeting">
                    <el-icon><Calendar /></el-icon>
                    安排批准会议
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="approval-status" v-if="approvalMeetingScheduled">
              <h4>批准状态</h4>
              <div class="status-items">
                <div class="status-item">
                  <el-icon><Document /></el-icon>
                  <span>文档已发送</span>
                  <el-tag 
                    :type="documentSent ? 'success' : 'warning'" 
                    size="small"
                  >
                    {{ documentSent ? '已发送' : '待发送' }}
                  </el-tag>
                </div>
                
                <div class="status-item">
                  <el-icon><Check /></el-icon>
                  <span>项目经理批准</span>
                  <el-tag 
                    :type="managerApproved ? 'success' : 'info'" 
                    size="small"
                  >
                    {{ managerApproved ? '已批准' : '待批准' }}
                  </el-tag>
                </div>
              </div>
              
              <div class="approval-actions">
                <el-button 
                  v-if="!documentSent"
                  type="primary" 
                  @click="sendDocument"
                >
                  发送文档
                </el-button>
                <el-button 
                  v-if="documentSent && !managerApproved"
                  type="success" 
                  @click="requestApproval"
                >
                  请求电子批准
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <div class="step-actions">
          <el-button size="large" @click="prevStep">
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="completeProcess"
            :disabled="!managerApproved"
          >
            完成流程
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 完成状态 -->
      <div v-if="currentStep === 2" class="completion-section">
        <el-result
          icon="success"
          title="必需可交付成果确定完成"
          sub-title="文档已获得批准，交付范围已明确"
        >
          <template #extra>
            <el-button type="primary" size="large" @click="goToOverview">
              返回规划中心
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Files, QuestionFilled, Plus, Calendar, Document, 
  Check, ArrowRight, ArrowLeft 
} from '@element-plus/icons-vue'
import { projectPlanningApi } from '@/api/pmpApi'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const standardDeliverables = ref([])
const customDeliverables = ref([])
const reviewMeetingForm = ref({
  subject: '[项目名称]:必需的项目管理可交付成果文档审查',
  invitee: '项目经理'
})
const approvalMeetingForm = ref({
  subject: '[项目名称]:必需的项目管理可交付成果文件批准',
  invitee: '项目经理'
})
const reviewMeetingScheduled = ref(false)
const approvalMeetingScheduled = ref(false)
const documentSent = ref(false)
const managerApproved = ref(false)

// 计算属性
const includedDeliverables = computed(() => {
  return standardDeliverables.value.filter(d => !d.excluded)
})

const excludedDeliverables = computed(() => {
  return standardDeliverables.value.filter(d => d.excluded)
})

// 页面挂载时加载数据
onMounted(() => {
  loadDeliverablesData()
})

// 加载可交付成果数据
const loadDeliverablesData = async () => {
  try {
    const response = await projectPlanningApi.getRequiredDeliverables()
    if (response.code === 200) {
      standardDeliverables.value = response.data.standardDeliverables
      customDeliverables.value = response.data.customDeliverables
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 可交付成果排除处理
const onDeliverableExclude = (deliverable) => {
  if (deliverable.excluded) {
    ElMessageBox.alert(
      '不使用标准可交付成果可能增加客户失败的风险。您确定要省略此可交付成果吗？',
      '风险提示',
      {
        confirmButtonText: '确认',
        type: 'warning'
      }
    ).then(() => {
      // 用户确认排除
    }).catch(() => {
      // 用户取消，恢复选择
      deliverable.excluded = false
    })
  } else {
    deliverable.reason = ''
  }
}

// 添加客户特定可交付成果
const addCustomDeliverable = () => {
  customDeliverables.value.push({
    name: '',
    description: ''
  })
}

// 删除客户特定可交付成果
const removeCustomDeliverable = (index) => {
  customDeliverables.value.splice(index, 1)
}

// 安排审查会议
const scheduleReviewMeeting = () => {
  reviewMeetingScheduled.value = true
  ElMessage.success('审查会议已安排')
}

// 生成文档草案
const generateDraft = () => {
  ElMessage.success('文档草案已生成')
  currentStep.value = 1
}

// 安排批准会议
const scheduleApprovalMeeting = () => {
  approvalMeetingScheduled.value = true
  ElMessage.success('批准会议已安排')
}

// 发送文档
const sendDocument = () => {
  documentSent.value = true
  ElMessage.success('文档已发送给项目经理')
}

// 请求批准
const requestApproval = () => {
  ElMessageBox.confirm(
    '确定要向项目经理请求电子批准吗？',
    '请求批准',
    {
      confirmButtonText: '发送请求',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    managerApproved.value = true
    ElMessage.success('项目经理已批准')
  })
}

// 完成流程
const completeProcess = () => {
  ElMessage.success('必需可交付成果确定流程已完成')
  currentStep.value = 2
}

// 显示帮助
const showHelp = (type) => {
  let content = ''
  if (type === 'review') {
    content = '此会议是与项目经理共同确定项目必需可交付成果的关键步骤。'
  }
  ElMessageBox.alert(content, '帮助信息')
}

// 步骤导航
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 返回概览
const goToOverview = () => {
  router.push('/project-planning')
}
</script>

<style scoped>
.required-deliverables {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 24px 0;
}

.deliverables-content {
  max-width: 1000px;
  margin: 0 auto;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.section-desc {
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.5;
}

.standard-deliverables, .custom-deliverables, .meeting-arrangement {
  margin: 24px 0;
}

.standard-deliverables h4, .custom-deliverables h4, .meeting-arrangement h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.deliverables-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.deliverable-item {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  transition: all 0.3s ease;
}

.deliverable-item.excluded {
  border-left-color: #e6a23c;
  background-color: #fdf6ec;
}

.deliverable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.deliverable-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.deliverable-name {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.exclusion-reason {
  margin-top: 12px;
}

.custom-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.custom-item {
  border-left: 4px solid #67c23a;
}

.custom-fields {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 16px;
  align-items: end;
}

.custom-actions {
  display: flex;
  justify-content: flex-end;
}

.document-summary {
  margin: 24px 0;
}

.document-summary h4 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.summary-content {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.summary-content h5 {
  color: #2c3e50;
  margin: 16px 0 8px 0;
}

.summary-content ul {
  margin: 0;
  padding-left: 20px;
}

.summary-content li {
  margin: 4px 0;
  color: #606266;
}

.excluded-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.excluded-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.excluded-name {
  font-weight: 500;
  color: #2c3e50;
}

.excluded-reason {
  color: #e6a23c;
  font-size: 14px;
}

.approval-status {
  margin: 24px 0;
}

.approval-status h4 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.approval-actions {
  display: flex;
  gap: 12px;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
}

.completion-section {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .deliverable-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .custom-fields {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
