<template>
  <div class="learning-center">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><School /></el-icon>
        学习中心
      </h1>
    </div>

    <div class="learning-content">
      <el-tabs v-model="activeTab" class="learning-tabs">
        <!-- 交互式指南 -->
        <el-tab-pane label="交互式指南" name="guides">
          <div class="guides-grid">
            <div 
              v-for="guide in learningData.interactiveGuides" 
              :key="guide.id"
              class="guide-card"
            >
              <el-card shadow="hover">
                <div class="guide-content">
                  <h4>{{ guide.title }}</h4>
                  <p>{{ guide.description }}</p>
                  <div class="guide-meta">
                    <el-tag size="small">{{ guide.duration }}</el-tag>
                    <el-tag :type="getDifficultyType(guide.difficulty)" size="small">
                      {{ guide.difficulty }}
                    </el-tag>
                    <el-tag v-if="guide.completed" type="success" size="small">
                      已完成
                    </el-tag>
                  </div>
                  <el-button type="primary" style="margin-top: 12px;">
                    开始学习
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 短视频 -->
        <el-tab-pane label="短视频" name="videos">
          <div class="videos-grid">
            <div 
              v-for="video in learningData.shortVideos" 
              :key="video.id"
              class="video-card"
            >
              <el-card shadow="hover">
                <div class="video-content">
                  <h4>{{ video.title }}</h4>
                  <p>{{ video.description }}</p>
                  <div class="video-meta">
                    <span>{{ video.duration }}</span>
                    <span>{{ video.views }} 次观看</span>
                  </div>
                  <el-button type="primary" style="margin-top: 12px;">
                    观看视频
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 知识卡片 -->
        <el-tab-pane label="知识卡片" name="cards">
          <div class="cards-grid">
            <div 
              v-for="card in learningData.knowledgeCards" 
              :key="card.id"
              class="knowledge-card"
            >
              <el-card shadow="hover">
                <div class="card-content">
                  <h4>{{ card.title }}</h4>
                  <p>{{ card.description }}</p>
                  <el-tag :type="getCategoryType(card.category)" size="small">
                    {{ getCategoryText(card.category) }}
                  </el-tag>
                  <el-button type="text" style="margin-top: 12px;">
                    查看详情
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { School } from '@element-plus/icons-vue'
import { knowledgeBaseApi } from '@/api/pmpApi'

const activeTab = ref('guides')
const learningData = ref({
  interactiveGuides: [],
  shortVideos: [],
  knowledgeCards: []
})

onMounted(async () => {
  const response = await knowledgeBaseApi.getLearningCenter()
  if (response.code === 200) {
    learningData.value = response.data
  }
})

const getDifficultyType = (difficulty) => {
  const types = {
    beginner: 'success',
    intermediate: 'warning',
    advanced: 'danger'
  }
  return types[difficulty] || 'info'
}

const getCategoryType = (category) => {
  const types = {
    planning: 'primary',
    meetings: 'success',
    monitoring: 'warning',
    general: 'info'
  }
  return types[category] || 'info'
}

const getCategoryText = (category) => {
  const texts = {
    planning: '规划',
    meetings: '会议',
    monitoring: '监控',
    general: '通用'
  }
  return texts[category] || '其他'
}
</script>

<style scoped>
.learning-center {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.learning-content {
  max-width: 1200px;
  margin: 0 auto;
}

.learning-tabs {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.guides-grid, .videos-grid, .cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.guide-card, .video-card, .knowledge-card {
  height: 100%;
}

.guide-content, .video-content, .card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.guide-content h4, .video-content h4, .card-content h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
}

.guide-content p, .video-content p, .card-content p {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.5;
  flex: 1;
}

.guide-meta, .video-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.video-meta span {
  color: #909399;
  font-size: 14px;
}
</style>
