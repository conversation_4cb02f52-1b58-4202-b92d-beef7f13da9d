<template>
  <div class="project-kickoff-overview">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Rocket /></el-icon>
        项目启动向导
      </h1>
      <p class="page-description">
        这是项目管理小白的"新手村"，引导您完成项目最初的关键几步。
      </p>
    </div>

    <div class="overview-content">
      <div class="module-grid">
        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('day-one-checklist')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><List /></el-icon>
            </div>
            <h3 class="card-title">首日任务清单</h3>
            <p class="card-description">
              项目管理新手的"第一步"，严格遵循第一天的管理操作指南，
              包含采访项目负责人、创建项目文件夹等关键任务。
            </p>
            <div class="card-stats">
              <el-tag type="info" size="small">5个任务</el-tag>
              <el-tag type="warning" size="small">预计3.5小时</el-tag>
            </div>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('client-method-discovery')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><Search /></el-icon>
            </div>
            <h3 class="card-title">客户方法发现会议</h3>
            <p class="card-description">
              将"安排客户方法发现会议的议程"转化为强制性的数字化工作流，
              确保完整收集项目规划所需的基础信息。
            </p>
            <div class="card-stats">
              <el-tag type="success" size="small">结构化流程</el-tag>
              <el-tag type="primary" size="small">防错设计</el-tag>
            </div>
          </div>
        </el-card>
      </div>

      <div class="progress-section">
        <h3 class="section-title">启动进度</h3>
        <div class="progress-content">
          <div class="progress-item">
            <div class="progress-label">
              <el-icon><List /></el-icon>
              <span>首日任务清单</span>
            </div>
            <el-progress 
              :percentage="dayOneProgress" 
              :color="getProgressColor(dayOneProgress)"
              :stroke-width="8"
            />
            <span class="progress-text">{{ dayOneProgress }}% 完成</span>
          </div>
          
          <div class="progress-item">
            <div class="progress-label">
              <el-icon><Search /></el-icon>
              <span>客户方法发现</span>
            </div>
            <el-progress 
              :percentage="clientMethodProgress" 
              :color="getProgressColor(clientMethodProgress)"
              :stroke-width="8"
            />
            <span class="progress-text">{{ clientMethodProgress }}% 完成</span>
          </div>
        </div>
      </div>

      <div class="quick-actions">
        <h3 class="section-title">快速操作</h3>
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="startDayOne">
            <el-icon><Play /></el-icon>
            开始首日任务
          </el-button>
          <el-button type="success" size="large" @click="scheduleDiscoveryMeeting">
            <el-icon><Calendar /></el-icon>
            安排发现会议
          </el-button>
          <el-button type="info" size="large" @click="viewHelp">
            <el-icon><QuestionFilled /></el-icon>
            查看帮助
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Rocket, List, Search, Play, Calendar, QuestionFilled 
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const dayOneProgress = ref(0)
const clientMethodProgress = ref(0)

// 页面挂载时获取进度数据
onMounted(() => {
  loadProgressData()
})

// 加载进度数据
const loadProgressData = () => {
  // 模拟从API获取进度数据
  setTimeout(() => {
    dayOneProgress.value = 20 // 模拟20%完成
    clientMethodProgress.value = 0 // 模拟0%完成
  }, 500)
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage === 0) return '#dcdfe6'
  if (percentage < 50) return '#e6a23c'
  if (percentage < 100) return '#409eff'
  return '#67c23a'
}

// 导航到模块
const navigateToModule = (module) => {
  router.push(`/project-kickoff/${module}`)
}

// 快速操作方法
const startDayOne = () => {
  router.push('/project-kickoff/day-one-checklist')
}

const scheduleDiscoveryMeeting = () => {
  router.push('/project-kickoff/client-method-discovery')
}

const viewHelp = () => {
  ElMessage.info('帮助功能正在开发中...')
}
</script>

<style scoped>
.project-kickoff-overview {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.overview-content {
  max-width: 1200px;
  margin: 0 auto;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.module-card:hover {
  transform: translateY(-4px);
  border-color: #409eff;
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.card-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.card-stats {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.progress-section, .quick-actions {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.progress-label {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 150px;
  font-weight: 500;
  color: #2c3e50;
}

.progress-text {
  min-width: 80px;
  text-align: right;
  color: #606266;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .module-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .progress-label {
    min-width: auto;
  }
  
  .progress-text {
    text-align: center;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
