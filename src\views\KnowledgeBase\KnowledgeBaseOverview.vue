<template>
  <div class="knowledge-base-overview">
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Reading /></el-icon>
        知识库与帮助中心
      </h1>
      <p class="page-description">
        项目管理知识的集中存储和学习中心。
      </p>
    </div>

    <div class="overview-content">
      <div class="module-grid">
        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('contextual-help')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><QuestionFilled /></el-icon>
            </div>
            <h3 class="card-title">上下文帮助</h3>
            <p class="card-description">
              根据当前操作提供相关的帮助信息和指导。
            </p>
          </div>
        </el-card>

        <el-card 
          class="module-card" 
          shadow="hover"
          @click="navigateToModule('learning-center')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon size="48"><School /></el-icon>
            </div>
            <h3 class="card-title">学习中心</h3>
            <p class="card-description">
              交互式指南、视频教程和知识卡片的学习平台。
            </p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Reading, QuestionFilled, School } from '@element-plus/icons-vue'

const router = useRouter()

// 导航到模块
const navigateToModule = (module) => {
  router.push(`/knowledge-base/${module}`)
}
</script>

<style scoped>
.knowledge-base-overview {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.page-description {
  font-size: 16px;
  color: #606266;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.overview-content {
  max-width: 1200px;
  margin: 0 auto;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.module-card:hover {
  transform: translateY(-4px);
  border-color: #409eff;
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 12px;
}

.card-description {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}
</style>
