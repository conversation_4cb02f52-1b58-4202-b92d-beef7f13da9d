@import './base.css';

/* 重置所有默认样式 */
#app {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

body {
  margin: 0;
  padding: 0;
  display: block;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    padding: 0;
  }
}
