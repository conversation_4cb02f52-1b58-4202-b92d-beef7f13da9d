<template>
  <div class="app-layout">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo-section">
            <el-icon size="32" color="#409eff"><ElementPlus /></el-icon>
            <h1 class="app-title">PMP ProGuide平台</h1>
          </div>
          
          <div class="header-actions">
            <el-button type="text" @click="showHelp">
              <el-icon><QuestionFilled /></el-icon>
              帮助
            </el-button>
            <el-button type="text" @click="showSettings">
              <el-icon><Setting /></el-icon>
              设置
            </el-button>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏导航 -->
        <el-aside :width="sidebarWidth" class="app-sidebar">
          <div class="sidebar-content">
            <!-- 折叠按钮 -->
            <div class="collapse-btn">
              <el-button 
                type="text" 
                @click="toggleSidebar"
                :icon="isCollapsed ? Expand : Fold"
              />
            </div>

            <!-- 导航菜单 -->
            <el-menu
              :default-active="activeMenu"
              :collapse="isCollapsed"
              :unique-opened="true"
              router
              class="sidebar-menu"
            >
              <!-- 项目启动向导 -->
              <el-sub-menu index="project-kickoff">
                <template #title>
                  <el-icon><Rocket /></el-icon>
                  <span>项目启动向导</span>
                </template>
                <el-menu-item index="/project-kickoff">
                  <el-icon><View /></el-icon>
                  <span>概览</span>
                </el-menu-item>
                <el-menu-item index="/project-kickoff/day-one-checklist">
                  <el-icon><List /></el-icon>
                  <span>首日任务清单</span>
                </el-menu-item>
                <el-menu-item index="/project-kickoff/client-method-discovery">
                  <el-icon><Search /></el-icon>
                  <span>客户方法发现会议</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 项目规划中心 -->
              <el-sub-menu index="project-planning">
                <template #title>
                  <el-icon><DataBoard /></el-icon>
                  <span>项目规划中心</span>
                </template>
                <el-menu-item index="/project-planning">
                  <el-icon><View /></el-icon>
                  <span>概览</span>
                </el-menu-item>
                <el-menu-item index="/project-planning/work-plan-builder">
                  <el-icon><Document /></el-icon>
                  <span>工作计划构建器</span>
                </el-menu-item>
                <el-menu-item index="/project-planning/schedule-release">
                  <el-icon><Calendar /></el-icon>
                  <span>计划进度表发布</span>
                </el-menu-item>
                <el-menu-item index="/project-planning/required-deliverables">
                  <el-icon><Files /></el-icon>
                  <span>必需可交付成果确定</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 执行与监控面板 -->
              <el-sub-menu index="execution-monitoring">
                <template #title>
                  <el-icon><Monitor /></el-icon>
                  <span>执行与监控面板</span>
                </template>
                <el-menu-item index="/execution-monitoring">
                  <el-icon><View /></el-icon>
                  <span>概览</span>
                </el-menu-item>
                <el-menu-item index="/execution-monitoring/health-status-dashboard">
                  <el-icon><TrendCharts /></el-icon>
                  <span>健康状态仪表盘</span>
                </el-menu-item>
                <el-menu-item index="/execution-monitoring/status-reports">
                  <el-icon><Document /></el-icon>
                  <span>状态报告</span>
                </el-menu-item>
                <el-menu-item index="/execution-monitoring/obstacle-management">
                  <el-icon><Warning /></el-icon>
                  <span>障碍管理</span>
                </el-menu-item>
                <el-menu-item index="/execution-monitoring/meeting-hub">
                  <el-icon><ChatRound /></el-icon>
                  <span>会议中心</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 变更管理流程 -->
              <el-sub-menu index="change-management">
                <template #title>
                  <el-icon><Switch /></el-icon>
                  <span>变更管理流程</span>
                </template>
                <el-menu-item index="/change-management">
                  <el-icon><View /></el-icon>
                  <span>概览</span>
                </el-menu-item>
                <el-menu-item index="/change-management/change-request">
                  <el-icon><Edit /></el-icon>
                  <span>变更请求</span>
                </el-menu-item>
                <el-menu-item index="/change-management/countdown-reminder">
                  <el-icon><Timer /></el-icon>
                  <span>10天倒计时提醒</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 知识库与帮助中心 -->
              <el-sub-menu index="knowledge-base">
                <template #title>
                  <el-icon><Reading /></el-icon>
                  <span>知识库与帮助中心</span>
                </template>
                <el-menu-item index="/knowledge-base">
                  <el-icon><View /></el-icon>
                  <span>概览</span>
                </el-menu-item>
                <el-menu-item index="/knowledge-base/contextual-help">
                  <el-icon><QuestionFilled /></el-icon>
                  <span>上下文帮助</span>
                </el-menu-item>
                <el-menu-item index="/knowledge-base/learning-center">
                  <el-icon><School /></el-icon>
                  <span>学习中心</span>
                </el-menu-item>
              </el-sub-menu>
            </el-menu>
          </div>
        </el-aside>

        <!-- 主内容区域 -->
        <el-main class="app-main">
          <div class="main-content">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-section" v-if="breadcrumbs.length > 0">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item 
                  v-for="(item, index) in breadcrumbs" 
                  :key="index"
                  :to="item.path"
                >
                  {{ item.title }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>

            <!-- 路由视图 -->
            <div class="router-view">
              <router-view />
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ElementPlus, QuestionFilled, Setting, Expand, Fold, Rocket, 
  DataBoard, Monitor, Switch, Reading, View, List, Search, 
  Document, Calendar, Files, TrendCharts, Warning, ChatRound, 
  Edit, Timer, School 
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const isCollapsed = ref(false)
const activeMenu = ref('')

// 计算属性
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '240px')

const breadcrumbs = computed(() => {
  const path = route.path
  const crumbs = []
  
  // 根据路径生成面包屑
  if (path.startsWith('/project-kickoff')) {
    crumbs.push({ title: '项目启动向导', path: '/project-kickoff' })
    if (path.includes('day-one-checklist')) {
      crumbs.push({ title: '首日任务清单', path: '/project-kickoff/day-one-checklist' })
    } else if (path.includes('client-method-discovery')) {
      crumbs.push({ title: '客户方法发现会议', path: '/project-kickoff/client-method-discovery' })
    }
  } else if (path.startsWith('/project-planning')) {
    crumbs.push({ title: '项目规划中心', path: '/project-planning' })
    if (path.includes('work-plan-builder')) {
      crumbs.push({ title: '工作计划构建器', path: '/project-planning/work-plan-builder' })
    } else if (path.includes('schedule-release')) {
      crumbs.push({ title: '计划进度表发布', path: '/project-planning/schedule-release' })
    } else if (path.includes('required-deliverables')) {
      crumbs.push({ title: '必需可交付成果确定', path: '/project-planning/required-deliverables' })
    }
  } else if (path.startsWith('/execution-monitoring')) {
    crumbs.push({ title: '执行与监控面板', path: '/execution-monitoring' })
    if (path.includes('health-status-dashboard')) {
      crumbs.push({ title: '健康状态仪表盘', path: '/execution-monitoring/health-status-dashboard' })
    } else if (path.includes('status-reports')) {
      crumbs.push({ title: '状态报告', path: '/execution-monitoring/status-reports' })
    } else if (path.includes('obstacle-management')) {
      crumbs.push({ title: '障碍管理', path: '/execution-monitoring/obstacle-management' })
    } else if (path.includes('meeting-hub')) {
      crumbs.push({ title: '会议中心', path: '/execution-monitoring/meeting-hub' })
    }
  } else if (path.startsWith('/change-management')) {
    crumbs.push({ title: '变更管理流程', path: '/change-management' })
    if (path.includes('change-request')) {
      crumbs.push({ title: '变更请求', path: '/change-management/change-request' })
    } else if (path.includes('countdown-reminder')) {
      crumbs.push({ title: '10天倒计时提醒', path: '/change-management/countdown-reminder' })
    }
  } else if (path.startsWith('/knowledge-base')) {
    crumbs.push({ title: '知识库与帮助中心', path: '/knowledge-base' })
    if (path.includes('contextual-help')) {
      crumbs.push({ title: '上下文帮助', path: '/knowledge-base/contextual-help' })
    } else if (path.includes('learning-center')) {
      crumbs.push({ title: '学习中心', path: '/knowledge-base/learning-center' })
    }
  }
  
  return crumbs
})

// 监听路由变化
watch(() => route.path, (newPath) => {
  activeMenu.value = newPath
}, { immediate: true })

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const showHelp = () => {
  ElMessage.info('帮助功能正在开发中...')
}

const showSettings = () => {
  ElMessage.info('设置功能正在开发中...')
}
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.app-header {
  background: white;
  border-bottom: 1px solid #ebeef5;
  padding: 0;
  height: 60px !important;
  line-height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 24px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.app-sidebar {
  background: white;
  border-right: 1px solid #ebeef5;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.collapse-btn {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  text-align: center;
}

.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}

.app-main {
  background-color: #f5f7fa;
  padding: 0;
  overflow-y: auto;
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.breadcrumb-section {
  background: white;
  padding: 12px 24px;
  border-bottom: 1px solid #ebeef5;
}

.router-view {
  flex: 1;
  overflow-y: auto;
}

/* 菜单项样式调整 */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
  color: #409eff;
}

:deep(.el-menu-item:hover) {
  background-color: #f5f7fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .breadcrumb-section {
    padding: 8px 16px;
  }
}
</style>
